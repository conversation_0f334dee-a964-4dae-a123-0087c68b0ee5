import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zones_editor_controller.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/zone_creation_controller.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/shape_test/widgets/zones_visualization_widget.dart';
import 'package:xoxknit/app/modules/shape_test/widgets/zone_creation_widget.dart';
import 'package:xoxknit/app/modules/shape_test/widgets/feedback_overlay_widget.dart';
import 'package:xoxknit/app/modules/shape_test/widgets/instruction_panel_widget.dart';

class ZonesEditorView extends StatefulWidget {
  final bool hideAppBar;

  const ZonesEditorView({
    super.key,
    this.hideAppBar = false,
  });

  @override
  State<ZonesEditorView> createState() => _ZonesEditorViewState();
}

class _ZonesEditorViewState extends State<ZonesEditorView> {
  final ScrollController _scrollController = ScrollController();
  late final ZonesEditorController _editorController;
  late final ZoneCreationController _creationController;

  // Define a unique tag for this instance's controllers
  final String _controllerTag =
      'zones_editor_${DateTime.now().millisecondsSinceEpoch}';

  @override
  void initState() {
    super.initState();

    // First register the editor controller
    _editorController = Get.put(ZonesEditorController(), tag: _controllerTag);

    // Then register the creation controller with a reference to the editor controller
    _creationController = Get.put(
        ZoneCreationController(editorController: _editorController),
        tag: _controllerTag);

    // Print debug info to verify controllers
    debugPrint(
        'Controllers initialized: editor=${_editorController.hashCode}, creation=${_creationController.hashCode}');
  }

  @override
  void dispose() {
    _scrollController.dispose();
    // Properly delete controllers in reverse order of dependency
    Get.delete<ZoneCreationController>(tag: _controllerTag);
    Get.delete<ZonesEditorController>(tag: _controllerTag);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shapeController = Get.find<ShapeEditorController>();
    final wizardController = Get.find<NewItemWizardController>();

    return Scaffold(
      appBar: widget.hideAppBar
          ? null
          : AppBar(
              actions: _buildAppBarActions(),
            ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: _buildPatternCanvas(shapeController, wizardController),
                ),
                const SizedBox(height: 16),
                // Instructional text
                InstructionPanelWidget(controller: _editorController),
              ],
            ),
          ),

          // Success feedback overlay
          FeedbackOverlayWidget(controller: _editorController),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions() {
    return [
      // Edit mode toggle
      Obx(() => IconButton(
            icon: Icon(_editorController.isEditingMode.value
                ? Icons.edit_off
                : Icons.edit),
            tooltip: _editorController.isEditingMode.value
                ? 'Exit Edit Mode'
                : 'Enter Edit Mode',
            onPressed: _editorController.toggleEditMode,
          )),

      // Create new zone - with debug text
      Obx(() => Visibility(
            visible: _editorController.isEditingMode.value &&
                !_editorController.isCreatingNewZone.value,
            child: IconButton(
              icon: const Icon(Icons.add_box),
              tooltip: 'Create New Zone',
              onPressed: () {
                debugPrint('Starting new zone creation');
                _editorController.startNewZoneCreation();
                debugPrint(
                    'Creation mode: ${_editorController.isCreatingNewZone.value}');
                // Force rebuild
                setState(() {});
              },
            ),
          )),

      // Delete selected zone
      Obx(() => Visibility(
            visible: _editorController.isEditingMode.value &&
                _editorController.selectedZoneIndex.value >= 0 &&
                !_editorController.isCreatingNewZone.value,
            child: IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'Delete Selected Zone',
              onPressed: _editorController.deleteSelectedZone,
            ),
          )),

      // Save button for new zone
      Obx(() => Visibility(
            visible: _editorController.isCreatingNewZone.value,
            child: IconButton(
                icon: const Icon(Icons.save),
                tooltip: 'Save New Zone',
                onPressed: () {
                  debugPrint('Saving zone');
                  _editorController.saveNewZone();
                  setState(() {});
                }),
          )),

      // Undo button for polygon creation
      Obx(() => Visibility(
            visible: _editorController.isCreatingNewZone.value,
            child: IconButton(
              icon: const Icon(Icons.undo),
              tooltip: 'Undo Last Point',
              onPressed: () {
                debugPrint('Undoing last point');
                _creationController.undoLastPoint();
                setState(() {});
              },
            ),
          )),

      // Cancel button for new zone
      Obx(() => Visibility(
            visible: _editorController.isCreatingNewZone.value,
            child: IconButton(
              icon: const Icon(Icons.cancel),
              tooltip: 'Cancel',
              onPressed: () {
                debugPrint('Canceling zone creation');
                _editorController.cancelZoneCreation();
                setState(() {});
              },
            ),
          )),

      // // Current mode indicator
      // Container(
      //   padding: const EdgeInsets.symmetric(horizontal: 8),
      //   alignment: Alignment.center,
      //   child: Text(
      //     _editorController.isCreatingNewZone.value
      //         ? 'Creating Zone'
      //         : 'Normal Mode',
      //     style: TextStyle(
      //       color: _editorController.isCreatingNewZone.value
      //           ? Colors.green
      //           : Colors.grey,
      //       fontWeight: FontWeight.bold,
      //       fontSize: 12,
      //     ),
      //   ),
      // ),
    ];
  }

  Widget _buildPatternCanvas(ShapeEditorController shapeController,
      NewItemWizardController wizardController) {
    // We need to be able to rebuild when state changes
    return StatefulBuilder(builder: (context, setState) {
      final instructions =
          shapeController.knittingInstructionsManager.currentInstructions.value;
      if (instructions.isEmpty) {
        return const Center(child: Text('No knitting pattern available'));
      }

      // Calculate aspect ratio based on the knitting gauge
      double aspectRatio = 0.75; // Default fallback
      final stitchesPerCm = wizardController.newItem.value.stitchesPerCm;
      final rowsPerCm = wizardController.newItem.value.rowsPerCm;

      if (rowsPerCm != null &&
          rowsPerCm > 0 &&
          stitchesPerCm != null &&
          stitchesPerCm > 0) {
        aspectRatio = stitchesPerCm / rowsPerCm;
      }

      // Calculate the pattern's intrinsic aspect ratio based on its dimensions
      final patternWidth = instructions.isNotEmpty ? instructions[0].length : 0;
      final patternHeight = instructions.length;

      // This is the physical layout ratio we need to maintain
      // The pattern needs to be laid out with physical dimensions
      // where width : height = (patternWidth * unit) : (patternHeight * unit/aspectRatio)
      // So the pattern's display ratio = (patternWidth/patternHeight) * aspectRatio
      final double patternDisplayRatio = patternWidth > 0 && patternHeight > 0
          ? (patternWidth / patternHeight) * aspectRatio
          : 1.0;

      // Debug output for state change
      debugPrint(
          'Building canvas: creation mode = ${_editorController.isCreatingNewZone.value}');

      // We'll let LayoutBuilder give us the available space
      return LayoutBuilder(
        builder: (context, constraints) {
          // Calculate maximum dimensions that fit in the available space
          // while maintaining the correct aspect ratio
          final availableWidth = constraints.maxWidth;
          final availableHeight = constraints.maxHeight;

          // Determine if width or height is the limiting factor
          final widthLimited =
              availableWidth / patternDisplayRatio < availableHeight;

          // Calculate optimal dimensions to fill available space
          double containerWidth;
          double containerHeight;

          if (widthLimited) {
            // Width is the limiting factor
            containerWidth = availableWidth;
            containerHeight = availableWidth / patternDisplayRatio;
          } else {
            // Height is the limiting factor
            containerHeight = availableHeight;
            containerWidth = availableHeight * patternDisplayRatio;
          }

          return SingleChildScrollView(
            controller: _scrollController,
            child: InteractiveViewer(
              boundaryMargin: const EdgeInsets.all(20.0),
              minScale: 0.5,
              maxScale: 6.0,
              // Add these two properties to allow two-finger gestures for zooming
              // and reserve single-finger gestures for zone creation
              panEnabled: !_editorController.isCreatingNewZone.value,
              scaleEnabled: true,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  width: containerWidth,
                  height: containerHeight,
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(4)),
                  child: Obx(() => _editorController.isCreatingNewZone.value
                      ? ZoneCreationWidget(
                          key: ValueKey(
                              'zone_creation_${DateTime.now().millisecondsSinceEpoch}'),
                          fullPattern: instructions,
                          selectionGrid: _editorController.newZoneSelection,
                          aspectRatio: aspectRatio,
                          onSelectionChanged:
                              _editorController.updateSelectionCell,
                          controller: _creationController,
                          existingZones: shapeController
                              .knittingInstructionsManager.knittingZones.value,
                          // Add a callback for zoom gesture detection
                          allowZoomGestures: true,
                        )
                      : Obx(() => ZonesVisualizationWidget(
                            fullPattern: instructions,
                            zones: shapeController.knittingInstructionsManager
                                .knittingZones.value,
                            selectedZoneIndex:
                                _editorController.selectedZoneIndex.value,
                            aspectRatio: aspectRatio,
                            isEditingMode:
                                _editorController.isEditingMode.value,
                            onZoneTap: (index) {
                              if (_editorController.isEditingMode.value) {
                                _editorController.selectedZoneIndex.value =
                                    index;
                              }
                            },
                            onZoneBoundaryEdit: _editorController
                                    .isEditingMode.value
                                ? (zoneIndex, edge, position) =>
                                    _editorController.updateZoneBoundary(
                                        zoneIndex,
                                        edge,
                                        position -
                                            (edge == 'left' || edge == 'right'
                                                ? _editorController
                                                    .shapeController
                                                    .knittingInstructionsManager
                                                    .knittingZones
                                                    .value[zoneIndex]
                                                    .startNeedle
                                                : _editorController
                                                    .shapeController
                                                    .knittingInstructionsManager
                                                    .knittingZones
                                                    .value[zoneIndex]
                                                    .startRow),
                                        edge == 'left' || edge == 'right')
                                : null,
                            controller: _editorController,
                          ))),
                ),
              ),
            ),
          );
        },
      );
    });
  }
}
