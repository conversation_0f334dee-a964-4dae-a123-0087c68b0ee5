import 'package:flutter/material.dart';
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';

/// Utility class for serializing and deserializing shape data
class ShapeSerializer {
  /// Convert a ShapeData object to JSON
  static Map<String, dynamic> shapeDataToJson(ShapeData shape) {
    final Map<String, dynamic> json = {
      'type': shape.type.index,
      'vertices': shape.vertices.map((v) => {'dx': v.dx, 'dy': v.dy}).toList(),
      'boundingRect': {
        'left': shape.boundingRect.left,
        'top': shape.boundingRect.top,
        'right': shape.boundingRect.right,
        'bottom': shape.boundingRect.bottom,
      },
      'center': {'dx': shape.center.dx, 'dy': shape.center.dy},
      'rotation': shape.rotation,
      'visualRotation': shape.visualRotation,
    };

    // Convert curve controls map to a serializable format
    if (shape.curveControls.isNotEmpty) {
      final curveControlsJson = <String, dynamic>{};
      shape.curveControls.forEach((key, value) {
        curveControlsJson[key.toString()] = {'dx': value.dx, 'dy': value.dy};
      });
      json['curveControls'] = curveControlsJson;
    }

    // Add grid coordinates if they exist (for device-independent positioning)
    if (shape.gridVertices != null) {
      json['gridVertices'] =
          shape.gridVertices!.map((v) => {'dx': v.dx, 'dy': v.dy}).toList();
    }
    if (shape.gridCenter != null) {
      json['gridCenter'] = {
        'dx': shape.gridCenter!.dx,
        'dy': shape.gridCenter!.dy
      };
    }
    if (shape.gridBoundingRect != null) {
      json['gridBoundingRect'] = {
        'left': shape.gridBoundingRect!.left,
        'top': shape.gridBoundingRect!.top,
        'right': shape.gridBoundingRect!.right,
        'bottom': shape.gridBoundingRect!.bottom,
      };
    }
    if (shape.gridCurveControls != null &&
        shape.gridCurveControls!.isNotEmpty) {
      final gridCurveControlsJson = <String, dynamic>{};
      shape.gridCurveControls!.forEach((key, value) {
        gridCurveControlsJson[key.toString()] = {
          'dx': value.dx,
          'dy': value.dy
        };
      });
      json['gridCurveControls'] = gridCurveControlsJson;
    }

    // Add group-specific properties if this is a GroupShapeData
    if (shape is GroupShapeData) {
      json['isGroup'] = true;

      // Convert child shapes to JSON
      json['childShapes'] =
          shape.childShapes.map((child) => shapeDataToJson(child)).toList();

      // Store original keys as strings
      json['originalKeys'] =
          shape.originalKeys.map((key) => key?.toString() ?? '').toList();
    }

    return json;
  }

  /// Convert a JSON object to a ShapeData object
  static ShapeData shapeDataFromJson(Map<String, dynamic> json) {
    // Parse basic properties
    final type = ShapeType.values[json['type']];
    final vertices = (json['vertices'] as List)
        .map((v) =>
            Offset((v['dx'] as num).toDouble(), (v['dy'] as num).toDouble()))
        .toList();
    final boundingRect = Rect.fromLTRB(
      (json['boundingRect']['left'] as num).toDouble(),
      (json['boundingRect']['top'] as num).toDouble(),
      (json['boundingRect']['right'] as num).toDouble(),
      (json['boundingRect']['bottom'] as num).toDouble(),
    );
    final center = Offset((json['center']['dx'] as num).toDouble(),
        (json['center']['dy'] as num).toDouble());
    final rotation = (json['rotation'] as num).toDouble();
    final visualRotation = json['visualRotation'] != null
        ? (json['visualRotation'] as num).toDouble()
        : 0.0;

    // Parse curve controls if present
    Map<int, Offset> curveControls = {};
    if (json.containsKey('curveControls')) {
      final curveControlsJson = json['curveControls'] as Map<String, dynamic>;
      curveControlsJson.forEach((key, value) {
        curveControls[int.parse(key)] = Offset(
            (value['dx'] as num).toDouble(), (value['dy'] as num).toDouble());
      });
    }

    // Parse grid coordinates if present
    List<Offset>? gridVertices;
    if (json.containsKey('gridVertices')) {
      gridVertices = (json['gridVertices'] as List)
          .map((v) =>
              Offset((v['dx'] as num).toDouble(), (v['dy'] as num).toDouble()))
          .toList();
    }

    Offset? gridCenter;
    if (json.containsKey('gridCenter')) {
      gridCenter = Offset(
        (json['gridCenter']['dx'] as num).toDouble(),
        (json['gridCenter']['dy'] as num).toDouble(),
      );
    }

    Rect? gridBoundingRect;
    if (json.containsKey('gridBoundingRect')) {
      gridBoundingRect = Rect.fromLTRB(
        (json['gridBoundingRect']['left'] as num).toDouble(),
        (json['gridBoundingRect']['top'] as num).toDouble(),
        (json['gridBoundingRect']['right'] as num).toDouble(),
        (json['gridBoundingRect']['bottom'] as num).toDouble(),
      );
    }

    Map<int, Offset>? gridCurveControls;
    if (json.containsKey('gridCurveControls')) {
      gridCurveControls = {};
      final gridCurveControlsJson =
          json['gridCurveControls'] as Map<String, dynamic>;
      gridCurveControlsJson.forEach((key, value) {
        gridCurveControls![int.parse(key)] = Offset(
            (value['dx'] as num).toDouble(), (value['dy'] as num).toDouble());
      });
    }

    // If this is a group, handle it specially
    if (json['isGroup'] == true) {
      // Parse child shapes
      final childShapes = (json['childShapes'] as List)
          .map((childJson) => shapeDataFromJson(childJson))
          .toList();

      // Parse original keys
      final originalKeys = (json['originalKeys'] as List)
          .map(
              (keyStr) => keyStr.isEmpty ? null : GlobalKey(debugLabel: keyStr))
          .toList();

      // Create and return a GroupShapeData
      return GroupShapeData(
        childShapes: childShapes,
        originalKeys: originalKeys,
        vertices: vertices,
        boundingRect: boundingRect,
        center: center,
        rotation: rotation,
        curveControls: curveControls,
        gridVertices: gridVertices,
        gridCenter: gridCenter,
        gridBoundingRect: gridBoundingRect,
        gridCurveControls: gridCurveControls,
        visualRotation: visualRotation,
      );
    }

    // Otherwise create a regular ShapeData
    return ShapeData(
      type: type,
      vertices: vertices,
      boundingRect: boundingRect,
      center: center,
      rotation: rotation,
      curveControls: curveControls,
      gridVertices: gridVertices,
      gridCenter: gridCenter,
      gridBoundingRect: gridBoundingRect,
      gridCurveControls: gridCurveControls,
      visualRotation: visualRotation,
    );
  }

  /// Convert a list of ShapeData objects to JSON
  static List<Map<String, dynamic>> shapesListToJson(List<ShapeData> shapes) {
    return shapes.map((shape) => shapeDataToJson(shape)).toList();
  }

  /// Convert a JSON list to a list of ShapeData objects
  static List<ShapeData> shapesListFromJson(List<dynamic> jsonList) {
    return jsonList.map((json) => shapeDataFromJson(json)).toList();
  }
}
