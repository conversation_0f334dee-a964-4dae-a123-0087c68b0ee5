// Constants for grid-related values used throughout the shape editor
import 'package:flutter/material.dart';

class GridConstants {
  // Private constructor to prevent instantiation
  GridConstants._();

  // Grid visual properties
  static const double gridSpacing = 20.0; // Base spacing for grid lines
  static const Color gridLineColor = Colors.grey;
  static const double gridLineWidth = 0.5;

  // Handle visual properties
  static const double defaultHandleSize = 10.0;
  static const double defaultVertexHandleSize =
      12.0; // Slightly larger vertex handles
  static const Color handleColor = Colors.blue;
  static const Color vertexHandleColor = Colors.red;
  static const Color edgeHandleColor = Colors.green;
  static const Color rotationHandleColor = Colors.purple;
  static const Color selectedHandleColor =
      Colors.orange; // Highlight for selected handles

  // Selection border properties
  static const Color selectedBorderColor = Colors.blueAccent;
  static const double selectedBorderWidth = 2.0;
  static const Color groupBorderColor = Colors.deepPurpleAccent;
  static const double groupBorderWidth = 2.5;

  // Zoom constraints
  static const double minZoomScale = 0.2;
  static const double maxZoomScale = 5.0;

  // Snapping thresholds (in logical pixels)
  static const double snapThreshold = 10.0; // Grid snapping
  static const double centerSnapThreshold = 15.0; // Centerline snapping
  static const double edgeSnapThreshold =
      8.0; // Shape-to-shape edge/vertex snapping

  // --- Minimum Shape Size --- (Logical Pixels)
  static const double minShapeSize = 20.0; // Minimum allowed width/height

  // Factor for extending the drawing area vertically beyond the screen
  static const double verticalExtendFactor = 10.0;

  /// Gets the extended height based on a base height
  static double getExtendedHeight(double baseHeight) {
    return baseHeight * verticalExtendFactor;
  }
}
