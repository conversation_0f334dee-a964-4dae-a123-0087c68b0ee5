import 'package:get/get.dart';
import 'package:xoxknit/app/data/models/wizard_state_model.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/wizard_state_service.dart';
import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';

class MyItemsController extends GetxController {
  final wizardStateService = WizardStateService.to;
  final items = <WizardStateModel>[].obs;
  final isLoading = false.obs;
  final showArchived = false.obs;

  // Track the last archived item for undo functionality
  WizardStateModel? _lastArchivedItem;

  @override
  void onInit() {
    super.onInit();
    loadItems();
  }

  Future<void> loadItems() async {
    try {
      if (isLoading.value) return;

      isLoading.value = true;
      final loadedItems = await wizardStateService.listWizardStates(
        archived: showArchived.value,
      );

      // Sort the loaded items:
      // 1. Active (non-completed) items at the top - sorted by date (newest first)
      // 2. Completed items at the bottom - sorted by date (newest first)
      loadedItems.sort((a, b) {
        // If completion status differs, place completed items at the bottom
        if (a.isCompleted != b.isCompleted) {
          return a.isCompleted ? 1 : -1;
        }
        // Otherwise sort by last modified date (newest first)
        return b.lastModified.compareTo(a.lastModified);
      });

      items.value = loadedItems;
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load items: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void toggleArchivedView() {
    showArchived.value = !showArchived.value;
    loadItems();
  }

  void continueItem(WizardStateModel item) {
    final parameters = {'stateId': item.id};
    final uri = Uri(
        path: Routes.MY_ITEMS + Routes.NEW_ITEM_WIZARD,
        queryParameters: parameters);

    // Navigate to the item in the wizard
    Get.rootDelegate.toNamed(uri.toString());

    // After navigation, use a delayed callback to ensure the controller is ready
    // This is a failsafe in case the _loadState method doesn't set the step correctly
    Future.delayed(const Duration(milliseconds: 500), () async {
      if (Get.isRegistered<NewItemWizardController>()) {
        final controller = Get.find<NewItemWizardController>();

        // If the item is completed, start from step 0, otherwise resume at the saved step
        if (item.isCompleted) {
          controller.currentStep.value = 0;
        } else {
          // Set the current step
          controller.currentStep.value = item.currentStep;

          // If we are resuming on step 3 (summary) or step 4 (interactive knitting),
          // ensure the knitting instructions are generated
          if (item.currentStep >= 3) {
            // Show loading indicator while generating knitting instructions
            controller.isLoading.value = true;
            try {
              // Generate knitting instructions for steps that need them
              await controller.generateKnittingInstructions();
            } catch (e) {
              // Handle error silently
            } finally {
              controller.isLoading.value = false;
            }
          } else if (item.currentStep == 2) {
            // If resuming at shape editor step, make sure shapes are loaded
            controller.isLoading.value = true;
            try {
              await controller.reloadShapesForShapeEditor();
            } catch (e) {
              // Handle error silently
            } finally {
              controller.isLoading.value = false;
            }
          }
        }
      }
    });
  }

  Future<void> archiveItem(WizardStateModel item) async {
    try {
      // Store the item before removing it from the list
      _lastArchivedItem = item;

      // Archive the item
      await wizardStateService.archiveWizardState(item.id);

      // Remove from the current list
      items.removeWhere((i) => i.id == item.id);

      // Show snackbar with undo option
      // Get.snackbar(
      //   'myItems_snackbar_itemArchived'.tr,
      //   'myItems_snackbar_archivedMessage'.tr,
      //   snackPosition: SnackPosition.BOTTOM,
      //   duration: const Duration(seconds: 5),
      //   mainButton: TextButton(
      //     child: Text(
      //       'myItems_snackbar_undoButton'.tr,
      //       style: const TextStyle(
      //           color: Colors.white, fontWeight: FontWeight.bold),
      //     ),
      //     onPressed: () {
      //       if (_lastArchivedItem != null) {
      //         undoArchive();
      //       }
      //     },
      //   ),
      // );
    } catch (e) {
      Get.snackbar(
        'common_error'.tr,
        'myItems_snackbar_archiveError'.tr + ': $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> undoArchive() async {
    if (_lastArchivedItem == null) return;

    try {
      await wizardStateService.unarchiveWizardState(_lastArchivedItem!.id);

      if (!showArchived.value) {
        final updatedItem =
            await wizardStateService.loadWizardState(_lastArchivedItem!.id);
        if (updatedItem != null && !updatedItem.isArchived) {
          items.add(updatedItem);
          items.sort((a, b) => b.lastModified.compareTo(a.lastModified));
        }
      }

      _lastArchivedItem = null;

      Get.snackbar(
        'myItems_snackbar_undoComplete'.tr,
        'myItems_snackbar_itemRestored'.tr,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      Get.snackbar(
        'common_error'.tr,
        'myItems_snackbar_unarchiveError'.tr + ': $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> unarchiveItem(WizardStateModel item) async {
    try {
      await wizardStateService.unarchiveWizardState(item.id);
      items.removeWhere((i) => i.id == item.id);
      Get.snackbar(
        'common_success'.tr,
        'myItems_snackbar_successUnarchived'.tr,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'common_error'.tr,
        '${'myItems_snackbar_unarchiveError'.tr}: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> deleteItem(WizardStateModel item) async {
    try {
      await wizardStateService.deleteWizardState(item.id);
      items.removeWhere((i) => i.id == item.id);
      Get.snackbar(
        'common_success'.tr,
        'myItems_snackbar_successDeleted'.tr,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 1),
      );
    } catch (e) {
      Get.snackbar(
        'common_error'.tr,
        '${'myItems_snackbar_deleteError'.tr}: $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  String getStepDescription(int step) {
    switch (step) {
      case 0:
        return 'myItems_stepDescription_itemDetails'.tr;
      case 1:
        return 'myItems_stepDescription_gaugeCalculator'.tr;
      case 2:
        return 'myItems_stepDescription_shapeEditor'.tr;
      case 3:
        return 'myItems_stepDescription_knittingZoneConfig'.tr;
      case 4:
        return 'myItems_stepDescription_patternSummary'.tr;
      case 5:
        return 'myItems_stepDescription_interactiveKnitting'.tr;
      default:
        return 'myItems_stepDescription_unknownStep'.tr;
    }
  }

  Future<void> markAsCompleted(WizardStateModel item) async {
    try {
      await wizardStateService.completeWizardState(item.id);

      // Update the item in the local list
      final index = items.indexWhere((i) => i.id == item.id);
      if (index >= 0) {
        final updatedItem = await wizardStateService.loadWizardState(item.id);
        if (updatedItem != null) {
          items[index] = updatedItem;

          // Re-sort the list to put completed items at the bottom
          loadItems();
        }
      }

      // Get.snackbar(
      //   'common_success'.tr,
      //   'myItems_snackbar_markedCompleted'.tr,
      //   snackPosition: SnackPosition.BOTTOM,
      // );
    } catch (e) {
      Get.snackbar(
        'common_error'.tr,
        'myItems_snackbar_markCompletedError'.tr + ': $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> markAsIncomplete(WizardStateModel item) async {
    try {
      // Get current state
      final state = await wizardStateService.loadWizardState(item.id);
      if (state == null) return;

      // Create updated state with isCompleted set to false
      final updatedState = state.copyWith(
        isCompleted: false,
        lastModified: DateTime.now(),
      );

      // Save the updated state
      await wizardStateService.saveWizardState(updatedState);

      // Update the item in the local list
      final index = items.indexWhere((i) => i.id == item.id);
      if (index >= 0) {
        final updatedItem = await wizardStateService.loadWizardState(item.id);
        if (updatedItem != null) {
          items[index] = updatedItem;

          // Re-sort the list
          loadItems();
        }
      }

      // Get.snackbar(
      //   'common_success'.tr,
      //   'myItems_snackbar_markedIncomplete'.tr,
      //   snackPosition: SnackPosition.BOTTOM,
      // );
    } catch (e) {
      Get.snackbar(
        'common_error'.tr,
        'myItems_snackbar_markIncompleteError'.tr + ': $e',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
