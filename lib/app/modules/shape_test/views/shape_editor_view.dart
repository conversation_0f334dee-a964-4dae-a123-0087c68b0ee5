import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/shape_test/views/unified_zones_editor_view.dart';

import '../widgets/zoom_indicator.dart';
import '../widgets/zoomable_board.dart';
import '../widgets/mirror_mode_overlay.dart';
import '../widgets/professional_grid.dart';
import '../widgets/expandable_toolbar.dart';
import '../widgets/global_tap_handler.dart';
import '../widgets/help_button.dart';

import '../widgets/shape_context_menu.dart';
import '../widgets/multi_select_panel.dart';
import '../widgets/custom_shape_creator.dart';

class ShapeEditorView extends StatelessWidget {
  const ShapeEditorView({super.key});

  @override
  Widget build(BuildContext context) {
    // Use GetBuilder for initial setup, Obx for reactive updates
    return GetBuilder<ShapeEditorController>(
      init: ShapeEditorController(),
      builder: (controller) {
        // Handle screen size changes when the view builds/rebuilds
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (controller.gridCoordinateConverter.screenSize !=
              Size(Get.width, Get.height)) {
            controller.handleScreenSizeChange();
          }
        });

        final screenSize = Size(Get.width, Get.height);

        return Scaffold(
          // Removed AppBar for cleaner look, add back if needed
          body: GlobalTapHandler(
            controller: controller,
            child: LayoutBuilder(
              builder: (context, constraints) {
                return Stack(
                  children: [
                    // Zoomable drawing board
                    ZoomableBoard(
                      controller: controller,
                      child: Stack(
                        children: [
                          // Professional grid with axis labels
                          ProfessionalGridWidget(
                            gridSystem: controller.gridSystem,
                            activeSnapInfo: controller.activeSnapInfo.value,
                            shapeStates: controller.shapeStates,
                          ),

                          // Mirror mode center line
                          Obx(() => controller.isMirrorModeActive.value
                              ? MirrorModeOverlay(
                                  controller: controller,
                                )
                              : const SizedBox()),

                          // Shape collection
                          GetBuilder<ShapeEditorController>(
                            builder: (controller) => Stack(
                              children: controller.shapes.map((shape) {
                                // Return only the MouseRegion and Shape
                                return MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: shape,
                                );
                              }).toList(),
                            ),
                          ),

                          // Custom Shape Preview (only visible when creating a custom shape)
                          Obx(() => controller.isCreatingCustomShape.value
                              ? CustomShapePreview(
                                  vertices: controller.customShapeVertices,
                                  connectToFirst:
                                      controller.shouldCloseCustomShape.value,
                                  selectedVertexIndex: controller
                                      .selectedCustomShapeVertexIndex.value,
                                  onVertexTap: (index) => controller
                                      .selectedCustomShapeVertexIndex
                                      .value = index,
                                  onVertexDragStart: (index) => controller
                                      .startCustomShapeVertexDrag(index),
                                  onVertexDragUpdate: (index, position) =>
                                      controller
                                          .updateCustomShapeVertexPosition(
                                              index, position),
                                  onVertexDragEnd: () =>
                                      controller.endCustomShapeVertexDrag(),
                                )
                              : const SizedBox()),

                          // Vertex Edit Handles (only visible when vertex edit mode is active and a shape is selected)
                          Obx(() {
                            if (!controller.isVertexEditModeActive.value ||
                                controller.selectedIndices.isEmpty) {
                              return const SizedBox();
                            }

                            final vertices =
                                controller.getSelectedShapeVertices();
                            if (vertices == null || vertices.isEmpty) {
                              return const SizedBox();
                            }

                            return VertexEditHandles(
                              vertices: vertices,
                              selectedVertexIndex:
                                  controller.selectedVertexIndex.value,
                              onVertexTap: (index) =>
                                  controller.selectedVertexIndex.value = index,
                              onVertexDragStart: (index, position) =>
                                  controller.startVertexDrag(index, position),
                              onVertexDragUpdate: (index, position) =>
                                  controller.updateVertexDragPosition(
                                      index, position),
                              onVertexDragEnd: () => controller.endVertexDrag(),
                            );
                          }),
                        ],
                      ),
                    ),

                    // Position ExpandableToolbar on the left side
                    Positioned(
                      left: 16,
                      bottom: 80, // Increased margin to avoid property HUD
                      child: ExpandableToolbar(
                        key: controller.toolbarKey,
                        controller: controller,
                      ),
                    ),

                    // Check if we're in the wizard flow or standalone mode
                    Obx(() {
                      // Try to find the NewItemWizardController
                      final bool isInWizardFlow =
                          Get.isRegistered<NewItemWizardController>() &&
                              NewItemWizardController.to.currentStep.value == 2;

                      if (isInWizardFlow) {
                        // We're in the wizard flow - show the wizard navigation buttons
                        return Container(); // These are shown by the wizard view
                      } else {
                        // We're in standalone mode - show the zones editor button
                        return Positioned(
                          right: 16,
                          bottom: 80,
                          child: FloatingActionButton(
                            onPressed: () async {
                              // Generate knitting instructions before navigating
                              await controller.knittingInstructionsManager
                                  .generateInstructions();
                              // Navigate to unified zones editor
                              Get.to(() => const UnifiedZonesEditorView());
                            },
                            backgroundColor: Colors.blue,
                            child: Icon(Icons.grid_view),
                            tooltip: 'interactiveKnitting_editKnittingZones'.tr,
                          ),
                        );
                      }
                    }),

                    // Zoom indicator
                    ZoomIndicator(controller: controller),

                    // Help button at top right corner
                    Positioned(
                      right: 16,
                      top: 0,
                      child: const HelpButton(isAppBar: false),
                    ),

                    // --- Context Menu ---
                    Obx(() => controller.isContextMenuVisible.value
                        ? ShapeContextMenu(
                            controller: controller,
                            onDismiss: () => controller.hideContextMenu(),
                          )
                        : const SizedBox()),

                    // Multi-select panel
                    Obx(() => controller.isMultiSelectionMode
                        ? Positioned(
                            right: 16,
                            top: 16,
                            child: MultiSelectPanel(
                              controller: controller,
                              panelKey: controller.multiSelectPanelKey,
                            ),
                          )
                        : const SizedBox()),

                    // Custom Shape Creator Dialog
                    Obx(() => controller.isCreatingCustomShape.value
                        ? CustomShapeCreator(
                            key: controller.customShapeCreatorKey,
                            controller: controller,
                            onCancel: () =>
                                controller.cancelCustomShapeCreation(),
                            onFinish: () =>
                                controller.finishCustomShapeCreation(),
                          )
                        : const SizedBox()),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }
}
