import 'package:flutter/cupertino.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:shopify_flutter/mixins/src/shopify_error.dart';
import 'package:shopify_flutter/shopify_flutter.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:xoxknit/app/components/app_loader.dart';
import 'package:xoxknit/app/core/exceptions/auth_exception.dart';
import 'package:xoxknit/app/core/utils/logger.dart';
import 'package:xoxknit/app/routes/app_pages.dart';
import 'package:xoxknit/app/services/route_service.dart';
import 'package:get_storage/get_storage.dart';
import '../data/models/user_model.dart';

class AuthService extends GetxService {
  final ShopifyAuth _shopifyAuth = ShopifyAuth.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final AppLogger _logger = AppLogger();

  final Rx<UserModel?> currentUser = Rx<UserModel?>(null);
  final RxBool isLoading = true.obs;
  final RxBool _isInitialized = false.obs;

  static AuthService get to => Get.find<AuthService>();

  bool get isInitialized => _isInitialized.value;

  bool get isLoggedIn => currentUser.value != null;

  @override
  void onInit() {
    super.onInit();

    _setupAuthListeners();
  }

  void _setupAuthListeners() {
    // Listen to Firebase auth state changes
    _firebaseAuth.authStateChanges().listen((firebaseUser) {
      _handleAuthStateChange(firebaseUser);
    });
  }

  Future<void> _handleAuthStateChange(User? firebaseUser) async {
    if (!_isInitialized.value) return; // Skip if not initialized

    try {
      if (firebaseUser == null) {
        currentUser.value = null;
        return;
      }

      // Verify Shopify session is still valid
      final shopifyCustomer = await _shopifyAuth.currentUser();
      if (shopifyCustomer == null) {
        // Shopify session expired but Firebase session exists
        await _firebaseAuth.signOut(); // Sign out from Firebase to sync states
        currentUser.value = null;
        return;
      }

      currentUser.value = _mapShopifyCustomerToUser(shopifyCustomer);
    } catch (e) {
      _logger.error('Error handling auth state change: $e');
      currentUser.value = null;
    }
  }

  /// Initialize the service and restore previous session if any
  Future<AuthService> init() async {
    try {
      isLoading.value = true;

      // Check for existing Firebase session
      final firebaseUser = _firebaseAuth.currentUser;

      if (firebaseUser != null) {
        try {
          // Verify Shopify session
          final shopifyCustomer = await _shopifyAuth.currentUser();

          if (shopifyCustomer != null) {
            currentUser.value = _mapShopifyCustomerToUser(shopifyCustomer);
            _logger.info('Restored existing user session');
          } else {
            // Shopify session expired, sign out from Firebase
            await _firebaseAuth.signOut();
            currentUser.value = null;
          }
        } catch (e) {
          _logger.error('Failed to verify Shopify session: $e');
          // On error, sign out from both services
          await _firebaseAuth.signOut();
          await _shopifyAuth.signOutCurrentUser();
          currentUser.value = null;
        }
      }

      _isInitialized.value = true;
      return this;
    } catch (e) {
      _logger.error('Failed to initialize auth service: $e');
      _isInitialized.value = true; // Still mark as initialized even on error
      return this;
    } finally {
      isLoading.value = false;
    }
  }

  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    ShopifyUser? shopifyCustomer;
    try {
      isLoading.value = true;

      // Authenticate with Shopify
      shopifyCustomer = await _shopifyAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Also authenticate with Firebase for additional features
      await _firebaseAuth.signInWithEmailAndPassword(
          email: email, password: dotenv.env['FIREBASE_SECRET_PASSWORD']!);

      currentUser.value = _mapShopifyCustomerToUser(shopifyCustomer);
      _logger.info('User signed in successfully: $email');

      _handlePostLoginRedirection();
    } on ShopifyException catch (e) {
      _logger.error('Shopify authentication failed \n $e');
      throw AuthException(
        code: 'shopify-auth-failed',
        message: _getReadableErrorMessage(e),
      );
    } on FirebaseAuthException catch (e) {
      if (e.code == 'invalid-credential') {
        // create the user's firebase account if user already exists in shopify
        try {
          await _firebaseAuth.createUserWithEmailAndPassword(
              email: email, password: dotenv.env['FIREBASE_SECRET_PASSWORD']!);

          currentUser.value = _mapShopifyCustomerToUser(shopifyCustomer!);
          _logger.info('User signed in successfully: $email');

          _handlePostLoginRedirection();
          return;
        } on FirebaseAuthException catch (e) {
          _logger.error('Firebase authentication failed \n ${e.code}');
          throw AuthException(
            code: e.code,
            message: _getReadableErrorMessage(e),
          );
        }
      }
      _logger.error('Firebase authentication failed \n ${e.code}');
      throw AuthException(
        code: e.code,
        message: _getReadableErrorMessage(e),
      );
    } catch (e) {
      _logger.error('Unexpected authentication error \n $e');
      throw AuthException(
        code: 'unknown',
        message: 'errors_unexpected'.tr,
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _handlePostLoginRedirection() {
    final routeService = RouteService.to;
    final intendedRoute = routeService.getAndClearIntendedRoute();
    final parameters = routeService.getAndClearIntendedParameters();

    // Ensure we're on the next frame before navigation
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Clear the current route stack before navigating
      Get.rootDelegate.offNamed(
        intendedRoute ?? Routes.HOME,
        arguments: parameters,
      );
    });
  }

  /// Create new account
  Future<void> createAccount(
      {required String email,
      required String password,
      required String firstName,
      required String lastName,
      required String phoneNumber}) async {
    try {
      isLoading.value = true;

      // Create Shopify account
      final shopifyCustomer = await _shopifyAuth.createUserWithEmailAndPassword(
        email: email,
        password: dotenv.env['FIREBASE_SECRET_PASSWORD']!,
        firstName: firstName,
        lastName: lastName,
        phone: phoneNumber,
      );

      // Create corresponding Firebase account
      await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      currentUser.value = _mapShopifyCustomerToUser(shopifyCustomer);
      _logger.info('New account created successfully: $email');
    } on ShopifyException catch (e) {
      _logger.error('Shopify account creation failed \n $e');
      throw AuthException(
        code: 'shopify-creation-failed',
        message: _getReadableErrorMessage(e),
      );
    } on FirebaseAuthException catch (e) {
      _logger.error('Firebase account creation failed \n $e');
      throw AuthException(
        code: e.code,
        message: _getReadableErrorMessage(e),
      );
    } catch (e) {
      _logger.error('Unexpected account creation error \n $e');
      throw AuthException(
        code: 'unknown',
        message: 'errors_unexpected'.tr,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Sign out current user
  void signOut() {
    Get.showOverlay(
      asyncFunction: () async {
        try {
          isLoading.value = true;

          // Track individual service signout success
          bool shopifySignOutSuccess = false;
          bool firebaseSignOutSuccess = false;

          // Try to sign out of Shopify
          try {
            await _shopifyAuth.signOutCurrentUser();
            shopifySignOutSuccess = true;
          } catch (e) {
            _logger.error('Shopify sign out failed: $e');
            Fluttertoast.showToast(msg: 'errors_partialSignOut'.tr);
          }

          // Try to sign out of Firebase
          try {
            await _firebaseAuth.signOut();
            firebaseSignOutSuccess = true;
          } catch (e) {
            _logger.error('Firebase sign out failed: $e');
            Fluttertoast.showToast(msg: 'errors_partialSignOut'.tr);
          }

          // Reset current user if at least one service signed out successfully
          if (shopifySignOutSuccess || firebaseSignOutSuccess) {
            currentUser.value = null;
            _logger.info(
                'User signed out (Shopify: $shopifySignOutSuccess, Firebase: $firebaseSignOutSuccess)');
          } else {
            throw AuthException(
              code: 'complete-signout-failed',
              message: 'auth_errors_signOutFailed'.tr,
            );
          }
        } catch (e) {
          _logger.error('Sign out failed: $e');
          Fluttertoast.showToast(msg: 'errors_signOutFailed'.tr);
        } finally {
          isLoading.value = false;
          // Always navigate to login screen, even if there were errors
          Get.rootDelegate.offNamed(Routes.LOGIN);
        }
      },
      loadingWidget: Center(
        child: AppLoader(
          color: Get.theme.colorScheme.onPrimary,
          size: 50,
        ),
      ),
    );
  }

  /// Send password reset email for given email
  Future<void> resetPassword(String email) async {
    try {
      isLoading.value = true;

      await _shopifyAuth.sendPasswordResetEmail(email: email);

      _logger.info('Password reset email sent to: $email');
    } catch (e) {
      _logger.error(
        'Password reset failed\n $e',
      );
      throw AuthException(
        code: 'reset-failed',
        message: 'auth_errors_resetFailed'.tr,
      );
    } finally {
      isLoading.value = false;
    }
  }

  /// Check if user has access to specific template
  Future<bool> hasTemplateAccess(String templateId) async {
    try {
      if (currentUser.value == null) return false;

      final accessData = await _shopifyAuth.currentCustomerAccessToken;
      if (accessData == null) return false;
      // Implementation depends on how template access is managed in Shopify
      // This is a placeholder implementation
      return accessData.contains(templateId);
    } catch (e) {
      _logger.error('Template access check failed,\n $e');
      return false;
    }
  }

  /// Deletes the user's account and all associated data from Firebase and Shopify
  Future<void> deleteUserAccount() async {
    try {
      isLoading.value = true;

      // Get current user before deletion
      final userId = currentUser.value?.id;
      if (userId == null) {
        throw AuthException(code: 'no-user', message: 'auth_errors_noUser'.tr);
      }

      // Delete from Shopify first - we need to use a custom API call
      // as deleteAccount isn't available directly in ShopifyAuth
      try {
        // Get the current customer access token to authenticate with Shopify
        final accessToken = await _shopifyAuth.currentCustomerAccessToken;
        if (accessToken == null) {
          throw AuthException(
              code: 'no-access-token', message: 'auth_errors_noAccessToken'.tr);
        }

        // Use Shopify GraphQL API to delete customer account
        // This typically requires a specific implementation depending on your Shopify setup
        // For now, we'll just sign out which will clear local Shopify data
        await _shopifyAuth.signOutCurrentUser();

        _logger.info('Shopify user session removed');
      } catch (e) {
        _logger.error('Failed to remove Shopify session: $e');
        // Continue with deletion even if Shopify fails
      }

      // Then delete from Firebase
      try {
        await _firebaseAuth.currentUser?.delete();
      } catch (e) {
        _logger.error('Failed to delete Firebase account: $e');
        throw AuthException(
            code: 'firebase-deletion-failed',
            message: 'auth_errors_firebaseDeletionFailed'.tr);
      }

      // Reset current user
      currentUser.value = null;
      _logger.info('User account successfully deleted');
    } catch (e) {
      _logger.error('Account deletion failed: $e');
      final errorMessage = e is AuthException
          ? e.message
          : 'auth_errors_unknownDeletionError'.tr;
      throw AuthException(
          code: 'account-deletion-failed',
          message: 'auth_errors_accountDeletionFailed'
              .trParams({'message': errorMessage}));
    } finally {
      isLoading.value = false;
    }
  }

  // Helper method to map Shopify customer to our UserModel
  UserModel _mapShopifyCustomerToUser(ShopifyUser customer) {
    return UserModel(
      id: customer.id!,
      firebaseId: _firebaseAuth.currentUser!.uid,
      email: customer.email ?? "",
      firstName: customer.firstName,
      lastName: customer.lastName,
      phone: customer.phone,
      acceptsMarketing: false,
    );
  }

  // Helper method to get readable auth error messages
  String _getReadableErrorMessage(dynamic error) {
    if (error is ShopifyException) {
      switch (error.errorKey) {
        case 'invalid_credentials':
          return 'auth_errors_invalidCredentials'.tr;
        case 'customer_disabled':
          return 'auth_errors_customerDisabled'.tr;
        case 'unconfirmed_email':
          return 'auth_errors_unconfirmedEmail'.tr;
        default:
          return error.errors?[0] ?? 'auth_errors_shopifyGeneric'.tr;
      }
    }

    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'user-not-found':
          return 'auth_errors_userNotFound'.tr;
        case 'wrong-password':
          return 'auth_errors_wrongPassword'.tr;
        case 'email-already-in-use':
          return 'auth_errors_emailInUse'.tr;
        case 'weak-password':
          return 'auth_errors_weakPassword'.tr;
        case 'operation-not-allowed':
          return 'auth_errors_operationNotAllowed'.tr;
        default:
          return error.message ?? 'auth_errors_firebaseGeneric'.tr;
      }
    }

    return 'auth_errors_unknownDeletionError'.tr;
  }

  bool isFirstLoad() {
    final box = GetStorage();
    final hasLaunched = box.read('has_launched') ?? false;
    if (!hasLaunched) {
      box.write('has_launched', true);
      return true;
    }
    return false;
  }
}
