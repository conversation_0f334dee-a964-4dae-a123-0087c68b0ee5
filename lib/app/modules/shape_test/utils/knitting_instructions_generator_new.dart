import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';

/// Converts shapes to a 2D boolean array representing a stitch pattern
/// where `true` means a stitch should be placed (shape is present) and `false` means no stitch (empty space).
///
/// This optimized function uses a rasterization approach for accurate shape rendering
/// that preserves all shape details including inward curves and fine details.
///
/// Parameters:
/// - `shapes`: List of ShapeData objects to convert
/// - `columns`: Fixed number of columns (typically matches needle count)
/// - `gridSize`: Size of each grid cell
/// - `gridSystem`: Optional GridSystem to use for accurate needle positioning
/// - `boundingBox`: Optional custom bounding box (calculated from shapes if not provided)
/// - `padding`: Optional padding to add around the shapes (default: 0)
/// - `aspectRatio`: The height-to-width ratio of stitches (default: 1.5)
///
/// Returns:
/// A 2D array where grid[row][column] is true if a shape is present at that cell.
List<List<bool>> convertShapesToStitchPattern({
  required List<ShapeData> shapes,
  required int? columns,
  required double cellWidth,
  dynamic gridSystem,
  Rect? boundingBox,
  double padding = 0,
  double aspectRatio = 0.8,
}) {
  // Determine the actual number of columns (needles)
  final int actualColumns = columns ?? 100;

  // Handle empty shapes list
  if (shapes.isEmpty) {
    return [List.generate(actualColumns, (_) => false)]; // Return one empty row
  }

  // Calculate the bounding rectangle for all shapes if not provided
  boundingBox ??= _calculateTotalBounds(shapes, padding);

  // Edge case: If the bounding box has zero width or height, adjust it
  if (boundingBox.width <= 0 || boundingBox.height <= 0) {
    boundingBox = Rect.fromCenter(
        center: boundingBox.center,
        width: math.max(boundingBox.width, cellWidth),
        height: math.max(boundingBox.height, cellWidth));
  }

  // Calculate row spacing based on aspect ratio
  final double validAspectRatio =
      (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
          ? 1.5
          : aspectRatio;
  final double rowSpacing = cellWidth * validAspectRatio; // Use aspect ratio

  // Calculate number of rows needed based on the bounding box height and new row spacing
  final rows = math.max(1, (boundingBox.height / rowSpacing).ceil());

  // Calculate the column offset based on the bounding box's left edge
  final int columnOffset = (boundingBox.left / cellWidth).floor();

  // Create a master path that combines all shapes
  final masterPath = _createMasterPath(shapes);

  // Check if the combined shape path is vertically symmetrical
  // We use the calculated boundingBox which includes padding
  final bool isSymmetrical =
      _isPathVerticallySymmetrical(masterPath, boundingBox);

  // Check if the combined shape path is horizontally symmetrical
  final bool isHorizontallySymmetrical =
      _isPathHorizontallySymmetrical(masterPath, boundingBox);

  // KnittingUtils.displayShapePath(masterPath);

  // Initialize grid with all cells empty
  final grid =
      List.generate(rows, (_) => List.generate(actualColumns, (_) => false));

  // Constants for sampling - adjusted for better precision
  const samplesPerCell = 12;
  const fillThreshold = 0.3; // Threshold to ensure better shape accuracy

  // Determine grid positioning based on whether a GridSystem is provided
  double gridLeft, gridTop;
  // Size canvasSize = Size(actualColumns * gridSize, rows * gridSize);

  gridLeft = boundingBox.left;
  gridTop = boundingBox.top;

  // Sample the master path with improved boundary detection
  for (var row = 0; row < rows; row++) {
    for (var col = 0; col < actualColumns; col++) {
      // Get actual column position based on grid system if available
      int effectiveCol = col;

      // Skip if column is out of range (based on needle mapping)
      if (effectiveCol < 0 || effectiveCol >= actualColumns) continue;

      // Calculate the target column in the output grid including the offset
      final int targetCol = effectiveCol + columnOffset;

      // Skip if the target column is outside the grid bounds
      if (targetCol < 0 || targetCol >= actualColumns) continue;

      // Calculate cell spatial coordinates using the effective column and row spacing
      final double cellLeftX = gridLeft + effectiveCol * cellWidth;
      final double cellTopY = gridTop + row * rowSpacing; // Use rowSpacing
      final double centerX = cellLeftX + (cellWidth / 2);
      final double centerY = cellTopY + (rowSpacing / 2); // Use rowSpacing

      // Check if the original targetCol was valid before potentially clamping.
      // Also ensure the clamped index is within bounds (redundant if targetCol < actualColumns, but safe).
      if (targetCol < 0 || targetCol >= actualColumns) continue;

      // If center point is in shape, mark as filled at the target column
      if (masterPath.contains(Offset(centerX, centerY))) {
        grid[row][targetCol] = true; // Use targetCol
        continue;
      }

      // Check four corner points of the cell using original effectiveCol and rowSpacing
      final corners = [
        Offset(cellLeftX, cellTopY), // Top-left
        Offset(cellLeftX + cellWidth, cellTopY), // Top-right
        Offset(
            cellLeftX, cellTopY + rowSpacing), // Bottom-left // Use rowSpacing
        Offset(cellLeftX + cellWidth,
            cellTopY + rowSpacing), // Bottom-right // Use rowSpacing
      ];

      bool cornerHit = corners.any((point) => masterPath.contains(point));

      // If center and corners are all outside, check edges with additional points
      if (!cornerHit) {
        // Add more edge points for better detection of thin shapes
        final edges = [
          // Standard edge midpoints
          Offset(centerX, cellTopY), // Top middle
          Offset(centerX,
              cellTopY + rowSpacing), // Bottom middle // Use rowSpacing
          Offset(cellLeftX, centerY), // Left middle
          Offset(cellLeftX + cellWidth, centerY), // Right middle

          // Additional quarter points for better edge detection
          Offset(cellLeftX + cellWidth * 0.25, centerY), // Left quarter
          Offset(cellLeftX + cellWidth * 0.75, centerY), // Right quarter
          Offset(centerX,
              cellTopY + rowSpacing * 0.25), // Top quarter // Use rowSpacing
          Offset(centerX,
              cellTopY + rowSpacing * 0.75), // Bottom quarter // Use rowSpacing
        ];

        bool edgeHit = edges.any((point) => masterPath.contains(point));

        // If no edge points hit, continue to next cell
        if (!edgeHit) continue;
      }

      // For boundary cells or close to shape, do detailed sampling using original effectiveCol and rowSpacing
      var hitCount = 0;
      var totalSamples = samplesPerCell * samplesPerCell;

      // Detailed sampling for cells that might have partial coverage
      for (var sy = 0; sy < samplesPerCell; sy++) {
        for (var sx = 0; sx < samplesPerCell; sx++) {
          final x = cellLeftX + ((sx + 0.5) * (cellWidth / samplesPerCell));
          final y = cellTopY +
              ((sy + 0.5) * (rowSpacing / samplesPerCell)); // Use rowSpacing

          if (masterPath.contains(Offset(x, y))) {
            hitCount++;
          }
        }
      }

      // Calculate coverage and set stitch if threshold is met
      final coverage = hitCount / totalSamples;

      // Only slightly reduce threshold for edge cells (check edge based on targetCol)
      // Note: Using targetCol for edge determination now.
      final isEdgeCell = targetCol == 0 ||
          targetCol == actualColumns - 1 ||
          row == 0 ||
          row == rows - 1;
      final effectiveThreshold =
          isEdgeCell ? fillThreshold * 0.9 : fillThreshold;

      if (coverage >= effectiveThreshold) {
        grid[row][targetCol] = true; // Use targetCol
      }
    }
  }

  // Post-processing to fill isolated empty cells
  _fillIsolatedEmptyCells(grid);

  // Ensure a minimum width of two stitches for stability
  _ensureMinimumStitchWidth(grid);

  // If the original shape was symmetrical, enforce symmetry in the grid
  if (isSymmetrical) {
    _enforceSymmetryInGrid(grid, actualColumns);
  }

  // If the original shape was horizontally symmetrical, enforce horizontal symmetry
  if (isHorizontallySymmetrical) {
    _enforceHorizontalSymmetryInGrid(grid);
  }

  // Optimize the grid by removing empty rows at the beginning and end
  final instructions = _optimizeGrid(grid, actualColumns);

  return instructions;
}

/// Checks if a path is vertically symmetrical around the center of its bounds.
/// Samples points on the left and right of the center line to verify containment.
bool _isPathVerticallySymmetrical(ui.Path path, Rect bounds,
    {double tolerance = 0.1}) {
  if (bounds.isEmpty || bounds.width <= tolerance) {
    return true; // Empty or very thin path is symmetrical
  }

  final double centerX = bounds.center.dx;
  const int numHeightSamples = 20; // Sample points along height
  const int numWidthSamples = 10; // Sample points along half-width

  for (int i = 0; i <= numHeightSamples; i++) {
    final double y = bounds.top + (bounds.height * i / numHeightSamples);

    for (int j = 1; j <= numWidthSamples; j++) {
      // Start from 1 to check points away from center
      final double dx = (bounds.width / 2) * j / numWidthSamples;
      // Add small epsilon to avoid landing exactly on boundary lines which can be ambiguous
      final Offset leftPoint = Offset(centerX - dx + 1e-6, y);
      final Offset rightPoint = Offset(centerX + dx - 1e-6, y);

      final bool containsLeft = path.contains(leftPoint);
      final bool containsRight = path.contains(rightPoint);

      if (containsLeft != containsRight) {
        // Debug print for asymmetry detection
        // print("Asymmetry detected at y=$y, dx=$dx. CenterX: $centerX, Left: $leftPoint (${containsLeft}), Right: $rightPoint (${containsRight})");
        return false;
      }
    }
  }
  return true;
}

/// Checks if a path is horizontally symmetrical around the center of its bounds.
/// Samples points above and below the center line to verify containment.
bool _isPathHorizontallySymmetrical(ui.Path path, Rect bounds,
    {double tolerance = 0.1}) {
  if (bounds.isEmpty || bounds.height <= tolerance) {
    return true; // Empty or very thin path is symmetrical
  }

  final double centerY = bounds.center.dy;
  const int numWidthSamples = 20; // Sample points along width
  const int numHeightSamples = 10; // Sample points along half-height

  for (int i = 0; i <= numWidthSamples; i++) {
    final double x = bounds.left + (bounds.width * i / numWidthSamples);

    for (int j = 1; j <= numHeightSamples; j++) {
      // Start from 1 to check points away from center
      final double dy = (bounds.height / 2) * j / numHeightSamples;
      // Add small epsilon to avoid landing exactly on boundary lines
      final Offset topPoint = Offset(x, centerY - dy + 1e-6);
      final Offset bottomPoint = Offset(x, centerY + dy - 1e-6);

      final bool containsTop = path.contains(topPoint);
      final bool containsBottom = path.contains(bottomPoint);

      if (containsTop != containsBottom) {
        // Debug print for asymmetry detection
        // print("Horizontal asymmetry detected at x=$x, dy=$dy. CenterY: $centerY, Top: $topPoint (${containsTop}), Bottom: $bottomPoint (${containsBottom})");
        return false;
      }
    }
  }
  return true;
}

/// Enforces vertical symmetry on the grid by mirroring the left half onto the right half.
/// Assumes L1/R1 are the two center columns for an even number of columns.
void _enforceSymmetryInGrid(List<List<bool>> grid, int actualColumns) {
  if (grid.isEmpty || actualColumns <= 1) return;

  // Assuming actualColumns is even based on L1/R1 description.
  // Midpoint index for the left half (L1).
  int midPoint1 = (actualColumns / 2).floor() - 1;

  for (int row = 0; row < grid.length; row++) {
    for (int col = 0; col <= midPoint1; col++) {
      int mirrorCol = actualColumns - 1 - col;
      if (mirrorCol >= 0 && mirrorCol < actualColumns) {
        // Bounds check for safety
        // Mirror the stitch state from the left column to the corresponding right column
        grid[row][mirrorCol] = grid[row][col];
      }
    }
    // If actualColumns were odd, the absolute center column (index actualColumns ~/ 2)
    // would remain untouched by this loop, preserving its original state.
  }
}

/// Enforces horizontal symmetry on the grid by mirroring the top half onto the bottom half.
void _enforceHorizontalSymmetryInGrid(List<List<bool>> grid) {
  if (grid.isEmpty || grid.length <= 1) return;

  final int rows = grid.length;
  // Midpoint index for the top half.
  int midPoint =
      (rows / 2).floor() - 1; // Index of the last row in the top half

  for (int row = 0; row <= midPoint; row++) {
    int mirrorRow = rows - 1 - row;
    if (mirrorRow >= 0 && mirrorRow < rows) {
      // Bounds check
      // Mirror the stitch state from the top row to the corresponding bottom row
      // Ensure the row lengths are the same before assignment
      if (grid[row].length == grid[mirrorRow].length) {
        grid[mirrorRow] =
            List.from(grid[row]); // Create a copy to avoid reference issues
      } else {
        // Handle potential inconsistencies if rows have different lengths (shouldn't happen with current logic)
        print(
            "Warning: Row length mismatch during horizontal symmetry enforcement. Row $row vs $mirrorRow");
        // As a fallback, just copy the boolean values up to the minimum length
        int minLength = math.min(grid[row].length, grid[mirrorRow].length);
        for (int col = 0; col < minLength; col++) {
          grid[mirrorRow][col] = grid[row][col];
        }
      }
    }
  }
  // If rows were odd, the absolute center row (index rows ~/ 2)
  // would remain untouched by this loop, preserving its original state.
}

/// Fill isolated empty cells that are surrounded by filled cells
void _fillIsolatedEmptyCells(List<List<bool>> grid) {
  if (grid.isEmpty || grid[0].isEmpty) return;

  final rows = grid.length;
  final cols = grid[0].length;

  // Create a copy of the grid to prevent affecting the iteration
  final gridCopy =
      List.generate(rows, (r) => List.generate(cols, (c) => grid[r][c]));

  for (int row = 1; row < rows - 1; row++) {
    for (int col = 1; col < cols - 1; col++) {
      // Only process empty cells
      if (gridCopy[row][col]) continue;

      // Count filled neighbors (all 8 surrounding cells)
      int filledNeighbors = 0;
      for (var dy = -1; dy <= 1; dy++) {
        for (var dx = -1; dx <= 1; dx++) {
          if (dx == 0 && dy == 0) continue; // Skip center

          if (gridCopy[row + dy][col + dx]) {
            filledNeighbors++;
          }
        }
      }

      // If 5 or more neighbors are filled, fill this cell too
      if (filledNeighbors >= 5) {
        grid[row][col] = true;
      }
    }
  }
}

/// Ensure a minimum horizontal width of two stitches for isolated stitches
void _ensureMinimumStitchWidth(List<List<bool>> grid) {
  if (grid.isEmpty || grid[0].isEmpty) return;

  final rows = grid.length;
  final cols = grid[0].length;

  // Iterate through each cell, skipping edges as they can't be isolated in the same way
  for (int row = 0; row < rows; row++) {
    // Need to handle the edges carefully
    for (int col = 0; col < cols; col++) {
      // Check if the current cell is a single stitch
      if (grid[row][col]) {
        bool leftNeighbor = (col > 0) && grid[row][col - 1];
        bool rightNeighbor = (col < cols - 1) && grid[row][col + 1];

        // If it's an isolated stitch (no horizontal neighbors)
        if (!leftNeighbor && !rightNeighbor) {
          // Prefer adding a stitch to the right if possible
          if (col < cols - 1) {
            grid[row][col + 1] = true;
          } else if (col > 0) {
            // Otherwise, add to the left (should only happen if it's the last column)
            grid[row][col - 1] =
                true; // This might modify a cell we already checked, but is generally safe
          }
          // If it's a single column grid, we can't add a neighbor, so it remains a single stitch.
        }
      }
    }
  }
}

/// Optimize the grid by removing empty rows at the beginning and end
List<List<bool>> _optimizeGrid(List<List<bool>> grid, int columns) {
  if (grid.isEmpty) return [List.generate(columns, (_) => false)];

  int firstStitchRow = -1;
  int lastStitchRow = -1;

  // Find first and last rows with stitches
  for (int i = 0; i < grid.length; i++) {
    if (grid[i].contains(true)) {
      if (firstStitchRow == -1) firstStitchRow = i;
      lastStitchRow = i;
    }
  }

  // No stitches found, return single empty row
  if (firstStitchRow == -1) {
    return [List.generate(columns, (_) => false)];
  }

  // Trim grid to only include rows with stitches
  final optimizedGrid = grid.sublist(firstStitchRow, lastStitchRow + 1);
  return optimizedGrid;
}

/// Calculate the total bounding box for all shapes
Rect _calculateTotalBounds(List<ShapeData> shapes, double padding) {
  double minX = double.infinity;
  double maxX = double.negativeInfinity;
  double minY = double.infinity;
  double maxY = double.negativeInfinity;

  for (final shape in shapes) {
    // Create a path for this shape to get accurate bounds including curves
    final path = _createShapePath(shape);
    final bounds = path.getBounds();

    // Update the overall bounds
    minX = math.min(minX, bounds.left);
    maxX = math.max(maxX, bounds.right);
    minY = math.min(minY, bounds.top);
    maxY = math.max(maxY, bounds.bottom);
  }

  // Apply padding
  if (padding > 0) {
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;
  }

  return Rect.fromLTRB(minX, minY, maxX, maxY);
}

/// Create a path for a single shape, properly handling rotation and curves
ui.Path _createShapePath(ShapeData shape) {
  // Handle grouped shapes by recursively creating paths for each child shape
  if (shape.runtimeType.toString().contains('GroupShapeData')) {
    // Using reflection to access child shapes since we don't have direct access to GroupShapeData
    final dynamic groupShape = shape;
    if (groupShape.childShapes != null) {
      final childShapes = groupShape.childShapes as List<ShapeData>;

      if (childShapes.isEmpty) return ui.Path();

      // Create a path for each child shape and combine them using union operation
      ui.Path? combinedPath;
      for (final childShape in childShapes) {
        final childPath = _createShapePath(childShape);

        if (combinedPath == null) {
          combinedPath = childPath;
        } else {
          try {
            combinedPath = Path.combine(
              PathOperation.union,
              combinedPath,
              childPath,
            );
          } catch (e) {
            // Fallback to addPath if Path.combine fails
            debugPrint(
                'Warning: Path.combine failed for group child shapes, falling back to addPath: $e');
            combinedPath!.addPath(childPath, Offset.zero);
          }
        }
      }
      return combinedPath ?? ui.Path();
    }
  }

  final path = ui.Path();

  // Skip shapes with no vertices
  if (shape.vertices.isEmpty) return path;

  // Get all vertices, applying rotation if needed
  final vertices = shape.rotation != 0
      ? shape.vertices
          .map((v) => _rotatePoint(v, shape.center, shape.rotation))
          .toList()
      : shape.vertices;

  // Move to the first vertex
  path.moveTo(vertices[0].dx, vertices[0].dy);

  // Draw each edge of the shape
  for (int i = 0; i < vertices.length; i++) {
    final nextIndex = (i + 1) % vertices.length;
    final nextVertex = vertices[nextIndex];

    // Check if this edge has a curve control point
    if (shape.curveControls.containsKey(i)) {
      // Calculate the midpoint of the edge
      final currentVertex = vertices[i];
      final midpoint = Offset((currentVertex.dx + nextVertex.dx) / 2,
          (currentVertex.dy + nextVertex.dy) / 2);

      // Get the control point offset from the midpoint
      final controlOffset = shape.curveControls[i]!;

      // Calculate the absolute control point position
      final controlPoint =
          midpoint.translate(controlOffset.dx, controlOffset.dy);

      // Apply rotation to the control point if needed
      final effectiveControlPoint = shape.rotation != 0
          ? _rotatePoint(controlPoint, shape.center, shape.rotation)
          : controlPoint;

      // Add a quadratic bezier curve to the path
      path.quadraticBezierTo(effectiveControlPoint.dx, effectiveControlPoint.dy,
          nextVertex.dx, nextVertex.dy);
    } else {
      // Add a straight line to the next vertex
      path.lineTo(nextVertex.dx, nextVertex.dy);
    }
  }

  // Close the path
  path.close();
  return path;
}

/// Create a combined path from all shapes using union operation to handle overlaps correctly
ui.Path _createMasterPath(List<ShapeData> shapes) {
  if (shapes.isEmpty) return ui.Path();

  // Start with the first shape
  ui.Path? combinedPath;

  for (final shape in shapes.reversed) {
    final shapePath = _createShapePath(shape);

    if (combinedPath == null) {
      // First shape - initialize the combined path
      combinedPath = shapePath;
    } else {
      // Combine paths using union operation to properly handle overlapping areas
      try {
        combinedPath = Path.combine(
          PathOperation.union,
          combinedPath,
          shapePath,
        );
      } catch (e) {
        // Fallback to addPath if Path.combine fails
        debugPrint('Warning: Path.combine failed, falling back to addPath: $e');
        combinedPath!.addPath(shapePath, Offset.zero);
      }
    }
  }

  return combinedPath ?? ui.Path();
}

/// Helper function to rotate a point around a center
Offset _rotatePoint(Offset point, Offset center, double angle) {
  // Calculate position relative to center
  final dx = point.dx - center.dx;
  final dy = point.dy - center.dy;

  // Apply rotation
  final cosTheta = math.cos(angle);
  final sinTheta = math.sin(angle);

  final rotatedDx = dx * cosTheta - dy * sinTheta;
  final rotatedDy = dx * sinTheta + dy * cosTheta;

  // Return point in original coordinate system
  return Offset(center.dx + rotatedDx, center.dy + rotatedDy);
}
