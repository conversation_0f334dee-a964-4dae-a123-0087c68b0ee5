import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

/// Custom hit testing widget for detecting taps on shaped objects
class CustomShapeHitTest extends SingleChildRenderObjectWidget {
  final Path path;

  const CustomShapeHitTest({
    super.key,
    required this.path,
    required super.child,
  });

  @override
  RenderShapeHitTest createRenderObject(BuildContext context) {
    return RenderShapeHitTest(path);
  }

  @override
  void updateRenderObject(
      BuildContext context, RenderShapeHitTest renderObject) {
    renderObject.path = path;
  }
}

/// Custom render object that performs hit testing against a Path
class RenderShapeHitTest extends RenderProxyBox {
  Path _path;

  RenderShapeHitTest(this._path);

  Path get path => _path;
  set path(Path value) {
    if (_path != value) {
      _path = value;
      markNeedsLayout();
    }
  }

  @override
  bool hitTest(BoxHitTestResult result, {required Offset position}) {
    // Create a slightly expanded version of the path for easier selection
    // This is done by checking a small area around the position
    const touchPadding = 10.0; // 10 pixels of touch padding

    // Check if the point is within the path or near its edges
    bool isHit = _path.contains(position);

    if (!isHit) {
      // Check points in a small radius around the original position
      for (double dx = -touchPadding; dx <= touchPadding; dx += 5) {
        for (double dy = -touchPadding; dy <= touchPadding; dy += 5) {
          final checkPoint = Offset(position.dx + dx, position.dy + dy);
          if (_path.contains(checkPoint)) {
            isHit = true;
            break;
          }
        }
        if (isHit) break;
      }
    }

    // Only proceed with hit testing if the point is within the expanded path
    if (isHit) {
      if (result.path.isEmpty) {
        result.add(BoxHitTestEntry(this, position));
      }
      return hitTestChildren(result, position: position);
    }
    return false;
  }
}

/// A utility class that wraps a shape painter and provides hit testing
class ShapeHitDetector extends StatelessWidget {
  final CustomPainter painter;
  final VoidCallback? onTap;
  final GestureDragUpdateCallback? onPanUpdate;
  final GestureDragStartCallback? onPanStart;
  final GestureDragEndCallback? onPanEnd;
  final Widget child;

  const ShapeHitDetector({
    super.key,
    required this.painter,
    this.onTap,
    this.onPanUpdate,
    this.onPanStart,
    this.onPanEnd,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // Ensure we have a valid path provider
    final Path path =
        painter is PathProvider ? (painter as PathProvider).getPath() : Path();

    // Make sure the path is valid before proceeding
    if (path.getBounds().isEmpty) {
      // If the path is invalid, return just the child without hit testing
      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        onPanUpdate: onPanUpdate,
        onPanStart: onPanStart,
        onPanEnd: onPanEnd,
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SizedBox(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              child: child,
            );
          },
        ),
      );
    }

    return CustomShapeHitTest(
      path: path,
      child: GestureDetector(
        behavior: HitTestBehavior.deferToChild,
        onTap: onTap,
        onPanUpdate: onPanUpdate,
        onPanStart: onPanStart,
        onPanEnd: onPanEnd,
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SizedBox(
              width: constraints.maxWidth,
              height: constraints.maxHeight,
              child: child,
            );
          },
        ),
      ),
    );
  }
}

/// Interface for painters that can provide a path for hit testing
abstract class PathProvider {
  Path getPath();
}
