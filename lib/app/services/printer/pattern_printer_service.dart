import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/utils/dimension_utils.dart';
import 'package:get/get.dart';

class PatternPrinterService {
  static final PatternPrinterService _instance =
      PatternPrinterService._internal();

  factory PatternPrinterService() {
    return _instance;
  }

  PatternPrinterService._internal();

  /// Print the knitting pattern
  Future<bool> printPattern({
    required NewItemModel itemData,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
    required Map<String, dynamic> patternStatistics,
    required BuildContext context,
  }) async {
    // Generate the PDF document
    final pdf = await _generatePDF(
      itemData: itemData,
      instructions: instructions,
      shapes: shapes,
      patternStatistics: patternStatistics,
    );

    // Print the document
    final printed = await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf,
      name:
          '${itemData.name ?? 'Knitting Pattern'}_${DateFormat('yyyyMMdd').format(DateTime.now())}',
      format: PdfPageFormat.a4,
    );

    return printed;
  }

  /// Generate the PDF document
  Future<Uint8List> _generatePDF({
    required NewItemModel itemData,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
    required Map<String, dynamic> patternStatistics,
  }) async {
    // Load the logo image
    final ByteData logoData =
        await rootBundle.load('assets/images/logo_white.png');
    final Uint8List logoBytes = logoData.buffer.asUint8List();
    final pw.MemoryImage logoImage = pw.MemoryImage(logoBytes);

    // Create a PDF document
    final pdf = pw.Document();

    // Calculate dimensions using DimensionUtils for consistency
    final topStitches = DimensionUtils.getTopStitches(instructions);
    final bottomStitches = DimensionUtils.getBottomStitches(instructions);
    final totalRows = DimensionUtils.getTotalRows(instructions);

    // Calculate real dimensions based on gauge using DimensionUtils
    final stitchesPerCm = itemData.stitchesPerCm ?? 2.0;
    final rowsPerCm = itemData.rowsPerCm ?? 2.5;

    // Use DimensionUtils to calculate cm values with standardized rounding
    final topWidthCm = DimensionUtils.stitchesToCm(topStitches, stitchesPerCm);
    final bottomWidthCm =
        DimensionUtils.stitchesToCm(bottomStitches, stitchesPerCm);
    final heightCm = DimensionUtils.rowsToCm(totalRows, rowsPerCm);

    // Use standardized formatting for consistent display
    final topWidthFormatted = DimensionUtils.formatCmForDisplay(topWidthCm);
    final bottomWidthFormatted =
        DimensionUtils.formatCmForDisplay(bottomWidthCm);
    final heightFormatted = DimensionUtils.formatCmForDisplay(heightCm);

    // Generate shape preview image
    final shapePreviewImage = await _captureShapePreview(
      topStitches: topStitches,
      bottomStitches: bottomStitches,
      rowsCount: totalRows,
      topWidth: topWidthFormatted,
      bottomWidth: bottomWidthFormatted,
      height: heightFormatted,
      instructions: instructions,
      shapes: shapes,
    );

    // Convert UI image to PDF image
    final pw.MemoryImage shapeImagePdf = pw.MemoryImage(shapePreviewImage);

    // Add the cover page
    pdf.addPage(_buildCoverPage(
      itemData: itemData,
      logoImage: logoImage,
      patternStatistics: patternStatistics,
      instructions: instructions,
      shapes: shapes,
      shapeImage: shapeImagePdf,
    ));

    // Add instruction pages
    _addInstructionPages(pdf, instructions, itemData, logoImage);

    // Return the PDF as bytes
    return pdf.save();
  }

  /// Capture shape preview as an image
  Future<Uint8List> _captureShapePreview({
    required int topStitches,
    required int bottomStitches,
    required int rowsCount,
    required String topWidth,
    required String bottomWidth,
    required String height,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
  }) async {
    return _createBasicShapeImage(topStitches, bottomStitches, rowsCount,
        topWidth, bottomWidth, height, instructions);
  }

  /// Create a basic shape image using Flutter's drawing capabilities
  Future<Uint8List> _createBasicShapeImage(
    int topStitches,
    int bottomStitches,
    int rowsCount,
    String topWidth,
    String bottomWidth,
    String height,
    List<List<bool>> instructions,
  ) async {
    // Create a picture recorder
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = const Size(280, 280);

    // Draw a white background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.white,
    );

    // Draw a simplified shape - either a trapezoid or based on instructions
    final paint = Paint()
      ..color = const Color(0xFFE6007E) // Theme color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final path = Path();

    // Define shape dimensions with padding
    const double padding = 40.0;
    final availableWidth = size.width - (padding * 2);
    final availableHeight = size.height - (padding * 2);

    if (instructions.isNotEmpty) {
      // Calculate shape from instructions
      final maxWidth = instructions.map((row) => row.length).reduce(math.max);
      final ratio = availableWidth / maxWidth;
      final rowHeight = availableHeight / instructions.length;

      // Find shape outline points, starting from top-left
      List<Offset> leftPoints = [];
      List<Offset> rightPoints = [];

      // Scan through all rows to get left and right edge points
      for (int i = 0; i < instructions.length; i++) {
        final row = instructions[i];

        // Find leftmost active stitch in this row
        int leftmost = -1;
        for (int j = 0; j < row.length; j++) {
          if (row[j]) {
            leftmost = j;
            break;
          }
        }

        // Find rightmost active stitch in this row
        int rightmost = -1;
        for (int j = row.length - 1; j >= 0; j--) {
          if (row[j]) {
            rightmost = j;
            break;
          }
        }

        if (leftmost >= 0) {
          final y = padding + i * rowHeight;
          leftPoints.add(Offset(padding + leftmost * ratio, y));
        }

        if (rightmost >= 0) {
          final y = padding + i * rowHeight;
          rightPoints.add(Offset(padding + rightmost * ratio, y));
        }
      }

      // Draw the path if we have points
      if (leftPoints.isNotEmpty && rightPoints.isNotEmpty) {
        // Start at top-left point
        path.moveTo(leftPoints.first.dx, leftPoints.first.dy);

        // Draw left edge (top to bottom)
        for (int i = 1; i < leftPoints.length; i++) {
          path.lineTo(leftPoints[i].dx, leftPoints[i].dy);
        }

        // Draw bottom edge (from left corner to right corner)
        path.lineTo(rightPoints.last.dx, rightPoints.last.dy);

        // Draw right edge (bottom to top)
        for (int i = rightPoints.length - 2; i >= 0; i--) {
          path.lineTo(rightPoints[i].dx, rightPoints[i].dy);
        }

        // Close the path back to start
        path.close();
      }
    } else {
      // Draw a trapezoid shape
      final ratio = topStitches / bottomStitches;
      final topW = availableWidth * ratio;

      path.moveTo(padding + (availableWidth - topW) / 2, padding);
      path.lineTo(padding + (availableWidth + topW) / 2, padding);
      path.lineTo(padding + availableWidth, padding + availableHeight);
      path.lineTo(padding, padding + availableHeight);
      path.close();
    }

    canvas.drawPath(path, paint);

    // Draw dimension lines
    _drawDimensionLines(canvas, size, padding, availableWidth, availableHeight);

    // Add dimension text
    _drawDimensionTexts(canvas, size, topWidth, bottomWidth, height,
        '$topStitches stitches', '$bottomStitches stitches', '$rowsCount rows');

    // Convert to image
    final picture = recorder.endRecording();
    final img = await picture.toImage(size.width.toInt(), size.height.toInt());
    final pngBytes = await img.toByteData(format: ui.ImageByteFormat.png);

    return pngBytes!.buffer.asUint8List();
  }

  /// Draw dimension lines on canvas
  void _drawDimensionLines(
      Canvas canvas, Size size, double padding, double width, double height) {
    final linePaint = Paint()
      ..color = const Color(0xFFE6007E) // Theme color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Top width dimension line
    canvas.drawLine(Offset(padding, padding - 10),
        Offset(padding + width, padding - 10), linePaint);

    // Top width ticks
    canvas.drawLine(
        Offset(padding, padding - 15), Offset(padding, padding - 5), linePaint);
    canvas.drawLine(Offset(padding + width, padding - 15),
        Offset(padding + width, padding - 5), linePaint);

    // Bottom width dimension line
    canvas.drawLine(Offset(padding, padding + height + 10),
        Offset(padding + width, padding + height + 10), linePaint);

    // Bottom width ticks
    canvas.drawLine(Offset(padding, padding + height + 5),
        Offset(padding, padding + height + 15), linePaint);
    canvas.drawLine(Offset(padding + width, padding + height + 5),
        Offset(padding + width, padding + height + 15), linePaint);

    // Height dimension line
    canvas.drawLine(Offset(padding - 10, padding),
        Offset(padding - 10, padding + height), linePaint);

    // Height ticks
    canvas.drawLine(
        Offset(padding - 15, padding), Offset(padding - 5, padding), linePaint);
    canvas.drawLine(Offset(padding - 15, padding + height),
        Offset(padding - 5, padding + height), linePaint);
  }

  /// Draw dimension texts on canvas
  void _drawDimensionTexts(
      Canvas canvas,
      Size size,
      String topWidth,
      String bottomWidth,
      String height,
      String topStitches,
      String bottomStitches,
      String rows) {
    const padding = 40.0;

    // Top dimension
    _drawCenteredText(
      canvas,
      '$topWidth cm',
      Offset(size.width / 2, padding - 20),
      fontSize: 12,
      isBold: true,
    );
    _drawCenteredText(
      canvas,
      topStitches,
      Offset(size.width / 2, padding - 35),
      fontSize: 10,
    );

    // Bottom dimension
    _drawCenteredText(
      canvas,
      '$bottomWidth cm',
      Offset(size.width / 2, size.height - padding + 20),
      fontSize: 12,
      isBold: true,
    );
    _drawCenteredText(
      canvas,
      bottomStitches,
      Offset(size.width / 2, size.height - padding + 35),
      fontSize: 10,
    );

    // Height dimension
    _drawCenteredText(
      canvas,
      '$height cm',
      Offset(padding - 25, size.height / 2),
      fontSize: 12,
      isBold: true,
      rotate: true,
    );
    _drawCenteredText(
      canvas,
      rows,
      Offset(padding - 25, size.height / 2 + 15),
      fontSize: 10,
    );
  }

  /// Draw centered text on canvas
  void _drawCenteredText(Canvas canvas, String text, Offset position,
      {double fontSize = 12, bool isBold = false, bool rotate = false}) {
    final textStyle = TextStyle(
      color: const Color(0xFFE6007E), // Theme color
      fontSize: fontSize,
      fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
    );

    final textSpan = TextSpan(
      text: text,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: ui.TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout();

    if (rotate) {
      canvas.save();
      canvas.translate(position.dx, position.dy);
      canvas.rotate(-math.pi / 2);
      textPainter.paint(
          canvas, Offset(-textPainter.width / 2, -textPainter.height / 2));
      canvas.restore();
    } else {
      textPainter.paint(
          canvas,
          Offset(position.dx - textPainter.width / 2,
              position.dy - textPainter.height / 2));
    }
  }

  /// Build the cover page with pattern summary
  pw.Page _buildCoverPage({
    required NewItemModel itemData,
    required pw.MemoryImage logoImage,
    required Map<String, dynamic> patternStatistics,
    required List<List<bool>> instructions,
    required List<ShapeData> shapes,
    required pw.MemoryImage shapeImage,
  }) {
    // Calculate dimensions for pattern summary using DimensionUtils for consistency
    final totalRows = DimensionUtils.getTotalRows(instructions);
    final topStitches = DimensionUtils.getTopStitches(instructions);
    final bottomStitches = DimensionUtils.getBottomStitches(instructions);

    // Format current date and time for the footer
    final now = DateTime.now();
    final formattedDate =
        DateFormat('yyyy-MM-dd HH:mm', Get.locale!.languageCode).format(now);

    return pw.Page(
      pageFormat: PdfPageFormat.a4,
      margin: const pw.EdgeInsets.all(20),
      build: (pw.Context context) {
        // Calculate available size
        final availableWidth = PdfPageFormat.a4.width - 40; // Minus margins
        final columnWidth = availableWidth / 2 -
            7.5; // Half width minus spacing between columns

        // Fixed heights for each row to ensure sections have the same height
        final row1Height = 100.0; // General info and machine info
        final row2Height = 120.0; // Description and yarn requirements
        final row3Height = 250.0; // Shape design and gauge calculation

        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // Header with title and logo
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      itemData.name ?? 'My XOXKITN pattern',
                      style: pw.TextStyle(
                        fontSize: 24,
                        color: PdfColor.fromHex('E6007E'),
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      'patternPrinter_knittingInstructions'.tr,
                      style: pw.TextStyle(
                        fontSize: 18,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text(
                      AuthService.to.currentUser.value!.fullName,
                      style: pw.TextStyle(
                        fontSize: 14,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                  ],
                ),
                pw.Image(logoImage, width: 80, height: 80),
              ],
            ),

            pw.SizedBox(height: 20),

            // Main content with information boxes - First row
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Left column - General info
                pw.Container(
                  width: columnWidth,
                  height: row1Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_generalInfo'.tr,
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow(
                                'patternPrinter_generalInfo'.tr, '$totalRows'),
                            _buildInfoRow('patternPrinter_machineType'.tr,
                                '${itemData.knittingMachine?.needlesCount ?? 148}'),
                            _buildInfoRow('patternPrinter_totalYarnRequired'.tr,
                                '${_calculateYarnRequirement(patternStatistics, itemData)}g'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                pw.SizedBox(width: 15),

                // Right column - Machine info
                pw.Container(
                  width: columnWidth,
                  height: row1Height,
                  child: _buildInfoBox(
                    title:
                        "${itemData.knittingMachine?.mainBrand ?? '-'} ${itemData.knittingMachine?.model ?? '-'}",
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow(
                                'patternPrinter_machineType'.tr,
                                itemData.knittingMachine?.model ??
                                    'Brother Milady FE265'),
                            _buildInfoRow('patternPrinter_stitchType'.tr,
                                itemData.stitchType ?? '-'),
                            _buildInfoRow('patternPrinter_tensionSetting'.tr,
                                itemData.tension ?? '4.2'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 15),

            // Main content with information boxes - Second row
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                // Left column - Item description
                // pw.Container(
                //   width: columnWidth,
                //   height: row2Height,
                //   child: _buildInfoBox(
                //     title: 'patternPrinter_itemDescription'.tr,
                //     content: [
                //       pw.SizedBox(
                //         width: columnWidth - 20,
                //         child: pw.Text(
                //           itemData.description ??
                //               'Darling little nose cover for people with noses that are overexposed to snow and sun',
                //           style: pw.TextStyle(
                //             fontSize: 12,
                //             color: PdfColors.grey800,
                //           ),
                //         ),
                //       ),
                //     ],
                //     expandContent: true,
                //   ),
                // ),

                // pw.SizedBox(width: 15),

                // Right column - Yarn requirements
                pw.Container(
                  width: availableWidth,
                  height: row2Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_yarnRequirements'.tr,
                    content: [
                      pw.SizedBox(
                        width: availableWidth,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.center,
                          children: [
                            _buildInfoRow(
                                'patternPrinter_yarnTitleAndStrands'.tr,
                                '${itemData.yarnTitle ?? '3/23'} - ${itemData.strands ?? 3} strands'),
                            _buildInfoRow('patternPrinter_supplier'.tr,
                                itemData.yarnSupplier ?? 'Schmendricksonn'),
                            _buildInfoRow('patternPrinter_totalYarnRequired'.tr,
                                '${_calculateYarnRequirement(patternStatistics, itemData)}g'),
                          ],
                        ),
                      ),
                    ],
                    expandContent: true,
                  ),
                ),
              ],
            ),

            pw.SizedBox(height: 15),

            // Shape design and Gauge calculation - Third row
            pw.Row(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Shape design (left)
                pw.Container(
                  width: columnWidth,
                  height: row3Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_shapeDesign'.tr,
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Center(
                          child: pw.SizedBox(
                            width: 200,
                            height: 200,
                            child: pw.Image(shapeImage),
                          ),
                        ),
                      ),
                    ],
                    expandContent: true,
                  ),
                ),

                pw.SizedBox(width: 15),

                // Gauge calculation (right)
                pw.Container(
                  width: columnWidth,
                  height: row3Height,
                  child: _buildInfoBox(
                    title: 'patternPrinter_gaugeCalculation'.tr,
                    content: [
                      pw.SizedBox(
                        width: columnWidth - 20,
                        child: pw.Column(
                          crossAxisAlignment: pw.CrossAxisAlignment.start,
                          children: [
                            // RESULTS section - header
                            pw.Text(
                              'patternPrinter_results'.tr,
                              style: pw.TextStyle(
                                fontSize: 12,
                                color: PdfColor.fromHex('E6007E'),
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 5),

                            // Results rows
                            pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.Text('patternPrinter_stitchesPer10Cm'.tr,
                                    style: pw.TextStyle(fontSize: 11)),
                                pw.Text(
                                    '${(itemData.stitchesPer10Cm ?? 20).toInt()}',
                                    style: pw.TextStyle(
                                        fontSize: 11,
                                        fontWeight: pw.FontWeight.bold)),
                              ],
                            ),
                            pw.SizedBox(height: 4),
                            pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                pw.Text('patternPrinter_rowsPer10Cm'.tr,
                                    style: pw.TextStyle(fontSize: 11)),
                                pw.Text(
                                    '${(itemData.rowsPer10Cm ?? 45).toInt()}',
                                    style: pw.TextStyle(
                                        fontSize: 11,
                                        fontWeight: pw.FontWeight.bold)),
                              ],
                            ),

                            pw.SizedBox(height: 15),

                            // MEASUREMENTS section - header
                            pw.Text(
                              'patternPrinter_measurements'.tr,
                              style: pw.TextStyle(
                                fontSize: 12,
                                color: PdfColor.fromHex('E6007E'),
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                            pw.SizedBox(height: 5),

                            // Measurements rows
                            _buildSimpleMeasurementRow(
                                'patternPrinter_swatchWidth'.tr,
                                '${itemData.swatchInfo?['width'] ?? "N/A"} cm'),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_swatchLength'.tr,
                                '${itemData.swatchInfo?['length'] ?? "N/A"} cm'),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_swatchWeight'.tr,
                                '${itemData.swatchInfo?['weight'] ?? "N/A"} g '),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_stitchesInSwatch'.tr,
                                '${itemData.swatchInfo?['stitches'] ?? "N/A"}'),
                            _buildSimpleMeasurementRow(
                                'patternPrinter_rowsInSwatch'.tr,
                                '${itemData.swatchInfo?['rows'] ?? "N/A"}'),
                          ],
                        ),
                      ),
                    ],
                    expandContent: true,
                  ),
                ),
              ],
            ),

            pw.Spacer(),

            // Footer
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  'patternPrinter_page'
                      .tr
                      .replaceAll('@current', '1')
                      .replaceAll('@total',
                          '${1 + _calculateInstructionPages(instructions)}'),
                  style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                ),
              ],
            ),

            pw.SizedBox(height: 5),

            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Text(
                  formattedDate,
                  style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  /// Add instruction pages to the PDF
  void _addInstructionPages(pw.Document pdf, List<List<bool>> instructions,
      NewItemModel itemData, pw.MemoryImage logoImage) {
    // Process instructions into row groups
    final rowGroups = _processInstructionsFromPattern(
        instructions, itemData.knittingMachine?.needlesCount ?? 100);

    // Create instruction tables with a maximum of 20 rows per page
    const int rowsPerPage = 20;

    // Format current date and time for footer
    final now = DateTime.now();
    final formattedDate = DateFormat('yyyy-MM-dd HH:mm').format(now);

    // Calculate total pages for instructions
    final int totalInstructionPages = (rowGroups.length / rowsPerPage).ceil();

    for (int i = 0; i < rowGroups.length; i += rowsPerPage) {
      final currentPageRows = rowGroups.skip(i).take(rowsPerPage).toList();
      final isLastPage = (i ~/ rowsPerPage) == totalInstructionPages - 1;

      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          margin: const pw.EdgeInsets.all(20),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Header with title, machine info, and logo
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    // Left side - pattern title
                    pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          itemData.name ?? 'My nose blanket',
                          style: pw.TextStyle(
                            fontSize: 24,
                            color: PdfColor.fromHex('E6007E'),
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          'patternPrinter_knittingInstructions'.tr,
                          style: pw.TextStyle(
                            fontSize: 18,
                            color: PdfColor.fromHex('E6007E'),
                          ),
                        ),
                        pw.SizedBox(height: 5),
                        pw.Text(
                          AuthService.to.currentUser.value?.fullName ??
                              'user name',
                          style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColor.fromHex('E6007E'),
                          ),
                        ),
                      ],
                    ),

                    // Center - machine info box
                    pw.Container(
                      width: 200,
                      child: _buildInfoBox(
                        title:
                            "${itemData.knittingMachine?.mainBrand ?? 'Doni\'s Brother'} ${itemData.knittingMachine?.model ?? 'chunky'}",
                        content: [
                          pw.SizedBox(
                            width: 180,
                            child: pw.Column(
                              crossAxisAlignment: pw.CrossAxisAlignment.start,
                              children: [
                                _buildInfoRow(
                                    'patternPrinter_machineType'.tr,
                                    itemData.knittingMachine?.model ??
                                        'Brother Milady FE265',
                                    fontSize: 10),
                                _buildInfoRow(
                                    'patternPrinter_stitchType'.tr, 'Jersey',
                                    fontSize: 10),
                                _buildInfoRow(
                                    'patternPrinter_tensionSetting'.tr,
                                    itemData.tension ?? '4.2',
                                    fontSize: 10),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Right side - logo
                    pw.Image(logoImage, width: 80, height: 80),
                  ],
                ),

                pw.SizedBox(height: 30),

                // Instructions Table
                _buildInstructionsTable(currentPageRows),

                // Add "Finished!" text if this is the last page
                if (isLastPage) ...[
                  pw.SizedBox(height: 20),
                  pw.Center(
                    child: pw.Text(
                      'patternPrinter_finished'.tr,
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                        color: PdfColor.fromHex('E6007E'),
                      ),
                    ),
                  ),
                ],

                pw.Spacer(),

                // Footer
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      'patternPrinter_page'
                          .tr
                          .replaceAll('@current', '${2 + i ~/ rowsPerPage}')
                          .replaceAll('@total',
                              '${1 + _calculateInstructionPages(instructions)}'),
                      style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                    ),
                  ],
                ),

                pw.SizedBox(height: 5),

                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.center,
                  children: [
                    pw.Text(
                      formattedDate,
                      style: pw.TextStyle(color: PdfColor.fromHex('E6007E')),
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      );
    }
  }

  /// Process instructions from the actual pattern to create row groups
  List<RowGroup> _processInstructionsFromPattern(
      List<List<bool>> instructions, int needleCount) {
    final List<RowGroup> rowGroups = [];

    if (instructions.isEmpty) {
      return rowGroups;
    }

    // Reverse the instructions to start from the last row
    final reversedInstructions = instructions.reversed.toList();

    // Analyze the pattern to find similar rows
    int currentRow = 0;
    while (currentRow < reversedInstructions.length) {
      final row = reversedInstructions[currentRow];

      // Find leftmost and rightmost active stitches to determine needle range
      int leftmostNeedle = -1;
      int rightmostNeedle = -1;

      for (int i = 0; i < row.length; i++) {
        if (row[i]) {
          leftmostNeedle = i;
          break;
        }
      }

      for (int i = row.length - 1; i >= 0; i--) {
        if (row[i]) {
          rightmostNeedle = i;
          break;
        }
      }

      // Skip if no active stitches found
      if (leftmostNeedle == -1 || rightmostNeedle == -1) {
        currentRow++;
        continue;
      }

      // Calculate center position of the needle bed
      final center = needleCount ~/ 2;

      // Convert to actual needle designations
      // Needles to the left of center are L needles (L1, L2, etc., increasing outward)
      // Needles to the right of center are R needles (R1, R2, etc., increasing outward)
      // Center is at position L1/R1
      String leftNeedleDesignation;
      String rightNeedleDesignation;
      int leftNeedleNumber;
      int rightNeedleNumber;

      // Determine left needle
      if (leftmostNeedle < center) {
        // Left side (L needles)
        leftNeedleDesignation = "L";
        leftNeedleNumber = center - leftmostNeedle;
      } else {
        // Right side (R needles)
        leftNeedleDesignation = "R";
        leftNeedleNumber = leftmostNeedle - center + 1;
      }

      // Determine right needle
      if (rightmostNeedle < center) {
        // Left side (L needles)
        rightNeedleDesignation = "L";
        rightNeedleNumber = center - rightmostNeedle;
      } else {
        // Right side (R needles)
        rightNeedleDesignation = "R";
        rightNeedleNumber = rightmostNeedle - center + 1;
      }

      // Find how many consecutive rows have the same needle distribution
      int consecutiveCount = 1;
      for (int i = currentRow + 1; i < reversedInstructions.length; i++) {
        final nextRow = reversedInstructions[i];

        // Check if next row has the same left and right boundaries
        int nextLeftmost = -1;
        int nextRightmost = -1;

        for (int j = 0; j < nextRow.length; j++) {
          if (nextRow[j]) {
            nextLeftmost = j;
            break;
          }
        }

        for (int j = nextRow.length - 1; j >= 0; j--) {
          if (nextRow[j]) {
            nextRightmost = j;
            break;
          }
        }

        if (nextLeftmost == leftmostNeedle &&
            nextRightmost == rightmostNeedle) {
          consecutiveCount++;
        } else {
          break;
        }
      }

      // Add the row group with new row numbering starting from 1
      rowGroups.add(
        RowGroup(
          startRow: currentRow + 1, // 1-indexed for display
          rowCount: consecutiveCount,
          leftNeedleDesignation: leftNeedleDesignation,
          leftNeedleNumber: leftNeedleNumber,
          rightNeedleDesignation: rightNeedleDesignation,
          rightNeedleNumber: rightNeedleNumber,
        ),
      );

      // Move to the next set of rows
      currentRow += consecutiveCount;
    }

    // If no row groups were found (which shouldn't happen), provide a default
    if (rowGroups.isEmpty) {
      rowGroups.addAll([
        RowGroup(
            startRow: 1,
            rowCount: 40,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 25,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 25),
        RowGroup(
            startRow: 41,
            rowCount: 6,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 32,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 32),
        RowGroup(
            startRow: 47,
            rowCount: 4,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 31,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 31),
        RowGroup(
            startRow: 51,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 30,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 30),
        RowGroup(
            startRow: 58,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 29,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 29),
        RowGroup(
            startRow: 65,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 28,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 28),
        RowGroup(
            startRow: 75,
            rowCount: 15,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 27,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 27),
        RowGroup(
            startRow: 90,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 25,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 25),
        RowGroup(
            startRow: 100,
            rowCount: 20,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 15,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 15),
        RowGroup(
            startRow: 120,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 25,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 25),
        RowGroup(
            startRow: 130,
            rowCount: 15,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 27,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 27),
        RowGroup(
            startRow: 145,
            rowCount: 10,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 28,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 28),
        RowGroup(
            startRow: 155,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 29,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 29),
        RowGroup(
            startRow: 162,
            rowCount: 7,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 30,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 30),
        RowGroup(
            startRow: 169,
            rowCount: 4,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 31,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 31),
        RowGroup(
            startRow: 173,
            rowCount: 6,
            leftNeedleDesignation: "L",
            leftNeedleNumber: 32,
            rightNeedleDesignation: "R",
            rightNeedleNumber: 32),
      ]);
    }

    return rowGroups;
  }

  /// Calculate total yarn requirement from statistics and item data
  String _calculateYarnRequirement(
      Map<String, dynamic> patternStatistics, NewItemModel itemData) {
    // Try to use actual data from pattern statistics if available
    if (patternStatistics.containsKey('totalStitches')) {
      final totalStitches = patternStatistics['totalStitches'] as int? ?? 0;
      final weightPer100CmSquared = itemData.weightPer100CmSquared ?? 0.0;

      if (totalStitches > 0 && weightPer100CmSquared > 0) {
        // Very rough estimation:
        // Assuming 100cm² contains stitches × rows per cm², and we know the weight
        final stitchesPerCm = itemData.stitchesPerCm ?? 2.0;
        final rowsPerCm = itemData.rowsPerCm ?? 2.5;
        final stitchesPerCm2 = stitchesPerCm * rowsPerCm;

        if (stitchesPerCm2 > 0) {
          // Calculate pattern area in square cm
          final patternArea = totalStitches / stitchesPerCm2;

          // Calculate weight based on weight per 100cm²
          final weight = (patternArea / 100) * weightPer100CmSquared;

          // Add 10% for waste
          final totalWeight = weight * 1.1;

          return totalWeight.toStringAsFixed(0);
        }
      }
    } else if (patternStatistics.containsKey('totalRows') &&
        patternStatistics.containsKey('maxWidth')) {
      // Fallback to rectangle-based calculation if totalStitches is not available
      final totalRows = patternStatistics['totalRows'] as int? ?? 0;
      final maxWidth = patternStatistics['maxWidth'] as int? ?? 0;
      final weightPer100CmSquared = itemData.weightPer100CmSquared ?? 0.0;
      final stitchesPerCm = itemData.stitchesPerCm ?? 2.0;
      final rowsPerCm = itemData.rowsPerCm ?? 2.5;

      if (totalRows > 0 &&
          maxWidth > 0 &&
          weightPer100CmSquared > 0 &&
          stitchesPerCm > 0 &&
          rowsPerCm > 0) {
        // Calculate area as a rectangle
        final widthInCm = maxWidth / stitchesPerCm;
        final heightInCm = totalRows / rowsPerCm;
        final areaInCmSquared = widthInCm * heightInCm;

        // Calculate weight with 10% waste
        final totalWeight =
            (areaInCmSquared / 100) * weightPer100CmSquared * 1.1;

        return totalWeight.toStringAsFixed(0);
      }
    }

    // If no calculation is possible, use the default
    return '370';
  }

  /// Build an info box with title and content
  pw.Widget _buildInfoBox({
    required String title,
    required List<pw.Widget> content,
    bool expandContent = false,
  }) {
    return pw.Stack(
      children: [
        pw.Container(
          margin: const pw.EdgeInsets.only(top: 10), // Make space for title
          padding: const pw.EdgeInsets.all(10),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColor.fromHex('E6007E'), width: 1),
            borderRadius: const pw.BorderRadius.all(pw.Radius.circular(10)),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.SizedBox(height: 5), // Add space for title at the top
              expandContent
                  ? pw.Expanded(
                      child: pw.Column(
                        crossAxisAlignment: pw.CrossAxisAlignment.start,
                        children: content,
                      ),
                    )
                  : pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: content,
                    ),
            ],
          ),
        ),
        // Title positioned on the border
        pw.Positioned(
          left: 20,
          top: 0,
          child: pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 8),
            color: PdfColors.white,
            child: pw.Text(
              title,
              style: pw.TextStyle(
                fontSize: 14,
                color: PdfColor.fromHex('E6007E'),
                fontWeight: pw.FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build an info row with label and value
  pw.Row _buildInfoRow(String label, String value, {double fontSize = 12}) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            fontSize: fontSize,
            color: PdfColor.fromHex('E6007E'),
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontSize: fontSize,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build instructions table
  pw.Widget _buildInstructionsTable(List<RowGroup> rowGroups) {
    // Define column widths
    final List<pw.TableColumnWidth> columnWidths = [
      const pw.FlexColumnWidth(1), // Start row
      const pw.FlexColumnWidth(2), // Rows to knit
      const pw.FlexColumnWidth(1.5), // Left needle setting
      const pw.FlexColumnWidth(1.5), // Right needle setting
    ];

    // Build table header
    final List<pw.TableRow> rows = [
      pw.TableRow(
        decoration: pw.BoxDecoration(color: PdfColor.fromHex('E6007E')),
        children: [
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_startRow'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_rowsToKnit'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_leftNeedleSetting'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
          pw.Padding(
            padding: const pw.EdgeInsets.all(8),
            child: pw.Text(
              'patternPrinter_rightNeedleSetting'.tr,
              style: pw.TextStyle(
                color: PdfColors.white,
                fontWeight: pw.FontWeight.bold,
              ),
              textAlign: pw.TextAlign.center,
            ),
          ),
        ],
      ),
    ];

    // Add data rows
    for (int i = 0; i < rowGroups.length; i++) {
      final group = rowGroups[i];
      rows.add(
        pw.TableRow(
          decoration: i % 2 == 0
              ? null
              : const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                '${group.startRow}',
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                'patternPrinter_knitRows'
                    .tr
                    .replaceAll('@count', '${group.rowCount}'),
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                '${group.leftNeedleDesignation}${group.leftNeedleNumber}',
                textAlign: pw.TextAlign.center,
              ),
            ),
            pw.Padding(
              padding: const pw.EdgeInsets.all(8),
              child: pw.Text(
                '${group.rightNeedleDesignation}${group.rightNeedleNumber}',
                textAlign: pw.TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    // Build table
    return pw.Table(
      columnWidths: {
        0: columnWidths[0],
        1: columnWidths[1],
        2: columnWidths[2],
        3: columnWidths[3],
      },
      border: pw.TableBorder.all(color: PdfColors.grey300),
      children: rows,
    );
  }

  /// Helper method to calculate instruction pages needed
  int _calculateInstructionPages(List<List<bool>> instructions) {
    final rowGroups = _processInstructionsFromPattern(instructions, 100);
    return (rowGroups.length / 20).ceil();
  }

  /// Build a simple measurement row for PDF output
  pw.Widget _buildSimpleMeasurementRow(String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 4),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Row(
            children: [
              pw.Container(
                width: 12,
                height: 12,
                decoration: pw.BoxDecoration(
                  shape: pw.BoxShape.circle,
                  border: pw.Border.all(color: PdfColor.fromHex('E6007E')),
                ),
                child: pw.Center(
                    child: pw.Text('?',
                        style: pw.TextStyle(
                            fontSize: 8, color: PdfColor.fromHex('E6007E')))),
              ),
              pw.SizedBox(width: 5),
              pw.Text(label, style: pw.TextStyle(fontSize: 10)),
            ],
          ),
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 10, vertical: 2),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex('FFCCE6'), // Very light pink
              borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
            ),
            child: pw.Text(
              value,
              style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}

/// Helper class for row grouping in the pattern
class RowGroup {
  final int startRow;
  final int rowCount;
  final String leftNeedleDesignation;
  final int leftNeedleNumber;
  final String rightNeedleDesignation;
  final int rightNeedleNumber;

  RowGroup({
    required this.startRow,
    required this.rowCount,
    required this.leftNeedleDesignation,
    required this.leftNeedleNumber,
    required this.rightNeedleDesignation,
    required this.rightNeedleNumber,
  });
}
