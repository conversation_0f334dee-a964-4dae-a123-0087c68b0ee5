import 'package:flutter/material.dart';

// Define the various shape types the app supports
// Kept simple with just a few basic shapes plus a group type
enum ShapeType {
  rectangle,
  triangle,
  rightTriangle,
  trapezoid,
  group, // Special type for when shapes are grouped together
  custom, // Custom polygon created by placing points
}

/// ShapeData stores all the information needed to render a shape
/// This is the core model class for the shape manipulation system
class ShapeData {
  // The type determines how we render and manipulate the shape
  final ShapeType type;

  // Vertices store the corner points of the shape
  // The order matters! Drawing proceeds from 0 to 1 to 2... etc.
  final List<Offset> vertices;

  // For bezier curves between vertices
  // Maps vertex index to control point offset from edge midpoint
  final Map<int, Offset> curveControls;

  // Calculated bounding rectangle - useful for hit detection and manipulation
  final Rect boundingRect;

  // Center point is used for rotation and other transformations
  final Offset center;

  // Rotation in radians - 0 means no rotation
  final double rotation;

  // New property to track the cumulative visual rotation applied, for display purposes
  final double visualRotation;

  // Grid-based coordinates for device-independent positioning
  // These represent positions in grid units rather than absolute pixels
  final List<Offset>? gridVertices;
  final Offset? gridCenter;
  final Rect? gridBoundingRect;
  final Map<int, Offset>? gridCurveControls;

  // Main constructor requires the essential data
  ShapeData({
    required this.type,
    required this.vertices,
    required this.boundingRect,
    required this.center,
    this.rotation = 0.0,
    this.visualRotation = 0.0,
    Map<int, Offset>? curveControls,
    this.gridVertices,
    this.gridCenter,
    this.gridBoundingRect,
    this.gridCurveControls,
  }) : curveControls = curveControls ?? {};

  // Immutable objects need a copyWith method for creating modified instances
  // This is crucial for the shape manipulation system
  ShapeData copyWith({
    ShapeType? type,
    List<Offset>? vertices,
    Map<int, Offset>? curveControls,
    Rect? boundingRect,
    Offset? center,
    double? rotation,
    double? visualRotation,
    List<Offset>? gridVertices,
    Offset? gridCenter,
    Rect? gridBoundingRect,
    Map<int, Offset>? gridCurveControls,
  }) {
    return ShapeData(
      type: type ?? this.type,
      vertices: vertices ?? this.vertices,
      curveControls: curveControls ?? this.curveControls,
      boundingRect: boundingRect ?? this.boundingRect,
      center: center ?? this.center,
      rotation: rotation ?? this.rotation,
      visualRotation: visualRotation ?? this.visualRotation,
      gridVertices: gridVertices ?? this.gridVertices,
      gridCenter: gridCenter ?? this.gridCenter,
      gridBoundingRect: gridBoundingRect ?? this.gridBoundingRect,
      gridCurveControls: gridCurveControls ?? this.gridCurveControls,
    );
  }

  @override
  String toString() {
    return 'ShapeData(type: $type, vertices: $vertices, boundingRect: $boundingRect, center: $center, rotation: $rotation, visualRotation: $visualRotation, gridVertices: $gridVertices, gridCenter: $gridCenter, gridBoundingRect: $gridBoundingRect, gridCurveControls: $gridCurveControls)';
  }

  // Factory for creating a rectangle shape - simplest shape with 4 corners
  factory ShapeData.rectangle(Rect rect) {
    return ShapeData(
      type: ShapeType.rectangle,
      vertices: [
        rect.topLeft,
        rect.topRight,
        rect.bottomRight,
        rect.bottomLeft,
      ],
      boundingRect: rect,
      center: rect.center,
      rotation: 0.0,
      visualRotation: 0.0,
    );
  }

  // Factory for creating an equilateral triangle
  // The triangle is positioned with its point at the top center
  factory ShapeData.triangle(Rect rect) {
    final center = rect.center;
    return ShapeData(
      type: ShapeType.triangle,
      vertices: [
        Offset(center.dx, rect.top), // Top point
        rect.bottomRight, // Bottom right
        rect.bottomLeft, // Bottom left
      ],
      boundingRect: rect,
      center: center,
      rotation: 0.0,
      visualRotation: 0.0,
    );
  }

  // Factory for creating a right triangle
  // Positioned with the right angle at the bottom left
  factory ShapeData.rightTriangle(Rect rect) {
    return ShapeData(
      type: ShapeType.rightTriangle,
      vertices: [
        rect.bottomLeft, // Right angle corner
        rect.bottomRight, // Bottom right
        rect.topLeft, // Top left
      ],
      boundingRect: rect,
      center: rect.center,
      rotation: 0.0,
      visualRotation: 0.0,
    );
  }

  // Factory for creating a trapezoid
  // Creates an inset at the top to form a trapezoid shape
  factory ShapeData.trapezoid(Rect rect) {
    final inset = rect.width * 0.2; // 20% inset on the top edge
    return ShapeData(
      type: ShapeType.trapezoid,
      vertices: [
        rect.topLeft.translate(inset, 0), // Top left with inset
        rect.topRight.translate(-inset, 0), // Top right with inset
        rect.bottomRight, // Bottom right
        rect.bottomLeft, // Bottom left
      ],
      boundingRect: rect,
      center: rect.center,
      rotation: 0.0,
      visualRotation: 0.0,
    );
  }

  double get width => boundingRect.width;
  double get height => boundingRect.height;

  ShapeData deepCopy() {
    return ShapeData(
      type: type,
      vertices: List<Offset>.from(vertices),
      curveControls: Map<int, Offset>.from(curveControls),
      boundingRect: boundingRect,
      center: center,
      rotation: rotation,
      visualRotation: visualRotation,
      gridVertices:
          gridVertices != null ? List<Offset>.from(gridVertices!) : null,
      gridCenter: gridCenter,
      gridBoundingRect: gridBoundingRect,
      gridCurveControls: gridCurveControls != null
          ? Map<int, Offset>.from(gridCurveControls!)
          : null,
    );
  }

  /// Convert absolute pixel coordinates to grid coordinates
  ShapeData toGridCoordinates(GridCoordinateConverter converter) {
    final newGridVertices =
        vertices.map((v) => converter.pixelToGrid(v)).toList();
    final newGridCenter = converter.pixelToGrid(center);
    final topLeft =
        converter.pixelToGrid(Offset(boundingRect.left, boundingRect.top));
    final bottomRight =
        converter.pixelToGrid(Offset(boundingRect.right, boundingRect.bottom));
    final newGridBoundingRect = Rect.fromPoints(topLeft, bottomRight);

    final newGridCurveControls = <int, Offset>{};
    for (final entry in curveControls.entries) {
      if (entry.value != Offset.zero) {
        newGridCurveControls[entry.key] =
            converter.pixelToGrid(entry.value, isVector: true);
      }
    }

    return copyWith(
      gridVertices: newGridVertices,
      gridCenter: newGridCenter,
      gridBoundingRect: newGridBoundingRect,
      gridCurveControls: newGridCurveControls,
    );
  }

  /// Convert grid coordinates back to absolute pixel coordinates for current screen size
  ShapeData fromGridCoordinates(GridCoordinateConverter converter) {
    // Only convert if we have grid coordinates
    if (gridVertices == null ||
        gridCenter == null ||
        gridBoundingRect == null) {
      return this;
    }

    final newVertices =
        gridVertices!.map((v) => converter.gridToPixel(v)).toList();
    final newCenter = converter.gridToPixel(gridCenter!);
    final topLeft = converter
        .gridToPixel(Offset(gridBoundingRect!.left, gridBoundingRect!.top));
    final bottomRight = converter
        .gridToPixel(Offset(gridBoundingRect!.right, gridBoundingRect!.bottom));
    final newBoundingRect = Rect.fromPoints(topLeft, bottomRight);

    final newCurveControls = <int, Offset>{};
    if (gridCurveControls != null) {
      for (final entry in gridCurveControls!.entries) {
        if (entry.value != Offset.zero) {
          newCurveControls[entry.key] =
              converter.gridToPixel(entry.value, isVector: true);
        }
      }
    } else {
      // Keep existing curve controls
      newCurveControls.addAll(curveControls);
    }

    return copyWith(
      vertices: newVertices,
      center: newCenter,
      boundingRect: newBoundingRect,
      curveControls: newCurveControls,
      visualRotation: visualRotation,
    );
  }

  Path getShapePath() {
    // Return empty path if shape data is invalid or insufficient vertices
    if (vertices.isEmpty) {
      return Path();
    }

    // Check if we have enough vertices for the shape type
    int minVertices = 3; // Most shapes need at least 3 vertices
    if (type == ShapeType.rectangle || type == ShapeType.trapezoid) {
      minVertices = 4;
    }

    if (vertices.length < minVertices) {
      // Not enough vertices for this shape type
      return Path();
    }

    // Create a new path based on the shape data
    final path = Path();

    // Move to first vertex
    path.moveTo(vertices[0].dx, vertices[0].dy);

    // Connect to rest of vertices
    for (int i = 1; i < vertices.length; i++) {
      final currentVertex = vertices[i];
      final prevIndex = i - 1; // Simplified from (i - 1) % vertices.length
      final prevVertex = vertices[prevIndex];

      // Check if we have a valid curve control for this edge
      if (curveControls.containsKey(prevIndex) &&
          curveControls[prevIndex] != Offset.zero) {
        // Calculate the midpoint of the edge
        final midpoint = Offset(
          (prevVertex.dx + currentVertex.dx) / 2,
          (prevVertex.dy + currentVertex.dy) / 2,
        );

        // Get the control point offset
        final controlOffset = curveControls[prevIndex]!;

        // Calculate the control point by adding the offset to the midpoint
        // The control point is relative to the midpoint of the edge
        final controlPoint = Offset(
          midpoint.dx + controlOffset.dx,
          midpoint.dy + controlOffset.dy,
        );

        // Draw the quadratic bezier curve
        path.quadraticBezierTo(
          controlPoint.dx,
          controlPoint.dy,
          currentVertex.dx,
          currentVertex.dy,
        );
      } else {
        // Simple line to the next vertex
        path.lineTo(currentVertex.dx, currentVertex.dy);
      }
    }

    // Handle the closing edge (from last vertex back to first)
    final lastIndex = vertices.length - 1;
    // Only attempt to close the path if we have a valid shape (at least 3 vertices)
    if (vertices.length >= 3) {
      if (curveControls.containsKey(lastIndex) &&
          curveControls[lastIndex] != Offset.zero) {
        // Calculate the midpoint of the closing edge
        final midpoint = Offset(
          (vertices[lastIndex].dx + vertices[0].dx) / 2,
          (vertices[lastIndex].dy + vertices[0].dy) / 2,
        );

        // Get the control point offset
        final controlOffset = curveControls[lastIndex]!;

        // Calculate the control point by adding the offset to the midpoint
        final controlPoint = Offset(
          midpoint.dx + controlOffset.dx,
          midpoint.dy + controlOffset.dy,
        );

        // Draw the quadratic bezier curve to close the path
        path.quadraticBezierTo(
          controlPoint.dx,
          controlPoint.dy,
          vertices[0].dx,
          vertices[0].dy,
        );
      } else {
        // Simple line to close the path
        path.close();
      }
    }

    // Apply rotation transformation after the path is created
    if (rotation != 0) {
      final matrix = Matrix4.identity()
        ..translate(center.dx, center.dy)
        ..rotateZ(rotation)
        ..translate(-center.dx, -center.dy);

      return path.transform(matrix.storage);
    }

    return path;
  }
}

/// Class to handle conversion between pixel coordinates and grid coordinates
class GridCoordinateConverter {
  final Size
      screenSize; // Screen size is needed as a reference for pixel scaling
  final int needleCount;
  final double
      aspectRatio; // Knitting aspect ratio (stitches_per_cm / rows_per_cm)

  // Base grid dimensions (abstract units)
  final double gridWidth;
  final double gridHeight;

  // Pixel dimensions of one 'cell' (stitch width x row height) at effective zoom=1
  // These are derived based on fitting needleCount across screenSize.width
  final double pixelWidthPerStitch;
  final double pixelHeightPerRow;

  GridCoordinateConverter({
    required this.screenSize,
    required this.needleCount,
    required this.aspectRatio, // Pass the knitting aspect ratio directly
  })  : gridWidth = needleCount.toDouble(),
        // Define abstract grid height based on width and knitting aspect ratio
        gridHeight = (aspectRatio > 0.001)
            ? needleCount.toDouble() / aspectRatio
            : needleCount.toDouble(), // Avoid division by zero
        // Calculate base pixel width for one stitch based on screen width
        pixelWidthPerStitch = screenSize.width / needleCount.toDouble(),
        // Calculate base pixel height for one row using knitting aspect ratio
        pixelHeightPerRow = (aspectRatio > 0.001)
            ? (screenSize.width / needleCount.toDouble()) *
                aspectRatio // pixels_per_stitch * aspect_ratio
            : (screenSize.width /
                needleCount.toDouble()) // Avoid division by zero, assume 1:1
  ;

  /// Convert pixel coordinates (relative to the top-left of the drawing area)
  /// to abstract grid coordinates (where gridY represents rows).
  Offset pixelToGrid(Offset pixelPos, {bool isVector = false}) {
    if (isVector) {
      // Convert a pixel delta to a grid delta (deltaNeedles, deltaRows)
      // Avoid division by zero if pixel dimensions are tiny
      double deltaNeedle =
          (pixelWidthPerStitch > 0.01) ? pixelPos.dx / pixelWidthPerStitch : 0;
      double deltaRow =
          (pixelHeightPerRow > 0.01) ? pixelPos.dy / pixelHeightPerRow : 0;
      return Offset(deltaNeedle, deltaRow);
    } else {
      // Convert a pixel position to a grid position (needle, row)
      // Normalize both axes relative to screen width, then scale to grid dimensions.
      // This makes gridY independent of screen aspect ratio changes.
      double gridX = (screenSize.width > 0.01)
          ? pixelPos.dx / screenSize.width * gridWidth
          : 0;
      double gridY = (screenSize.width > 0.01)
          ? pixelPos.dy / screenSize.width * gridHeight
          : 0; // Use screenSize.width for normalization
      return Offset(gridX, gridY);
    }
  }

  /// Convert abstract grid coordinates (where gridY represents rows)
  /// back to pixel coordinates (relative to the top-left of the drawing area).
  Offset gridToPixel(Offset gridPos, {bool isVector = false}) {
    if (isVector) {
      // Convert a grid delta (deltaNeedles, deltaRows) to a pixel delta
      double pixelDeltaX = gridPos.dx * pixelWidthPerStitch;
      double pixelDeltaY = gridPos.dy * pixelHeightPerRow;
      return Offset(pixelDeltaX, pixelDeltaY);
    } else {
      // Convert a grid position (needle, row) to a pixel position.
      // Scale both axes relative to screen width.
      // This correctly maps gridY (rows) back to pixel space regardless of screen aspect ratio.
      double pixelX =
          (gridWidth > 0.01) ? gridPos.dx / gridWidth * screenSize.width : 0;
      double pixelY = (gridHeight > 0.01)
          ? gridPos.dy / gridHeight * screenSize.width
          : 0; // Use screenSize.width for scaling
      return Offset(pixelX, pixelY);
    }
  }
}
