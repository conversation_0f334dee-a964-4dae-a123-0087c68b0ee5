import 'package:flutter/material.dart';
import '../models/shape_data.dart';

class ShapePathVisualizer extends StatelessWidget {
  final ShapeData shapeData;
  final Path? path;
  final double size;

  const ShapePathVisualizer({
    super.key,
    required this.shapeData,
    this.size = 200,
    this.path,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Si<PERSON>(size, size),
      painter: <PERSON>hape<PERSON>athPainter(shapeData: shapeData, path: path),
    );
  }
}

class ShapePathPainter extends CustomPainter {
  final ShapeData shapeData;
  final Path? path;

  ShapePathPainter({required this.shapeData, this.path});

  @override
  void paint(Canvas canvas, Size size) {
    // Draw the shape's path
    final path = this.path ?? shapeData.getShapePath();

    // Create a paint for the shape fill
    final shapePaint = Paint()
      ..color = Colors.blue.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    // Create a paint for the path outline
    final pathPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // Draw the shape
    canvas.drawPath(path, shapePaint);

    // Draw the path outline
    canvas.drawPath(path, pathPaint);

    // Draw vertices as dots
    final vertexPaint = Paint()
      ..color = Colors.green
      ..style = PaintingStyle.fill;

    for (final vertex in shapeData.vertices) {
      canvas.drawCircle(vertex, 4, vertexPaint);
    }

    // Draw control points if they exist
    final controlPaint = Paint()
      ..color = Colors.orange
      ..style = PaintingStyle.fill;

    for (int i = 0; i < shapeData.vertices.length; i++) {
      if (shapeData.curveControls.containsKey(i)) {
        final controlOffset = shapeData.curveControls[i]!;
        final currentVertex = shapeData.vertices[i];
        final nextVertex =
            shapeData.vertices[(i + 1) % shapeData.vertices.length];

        final midpoint = Offset(
          (currentVertex.dx + nextVertex.dx) / 2,
          (currentVertex.dy + nextVertex.dy) / 2,
        );

        final controlPoint = Offset(
          midpoint.dx + controlOffset.dx,
          midpoint.dy + controlOffset.dy,
        );

        canvas.drawCircle(controlPoint, 4, controlPaint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
