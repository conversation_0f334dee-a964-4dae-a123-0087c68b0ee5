import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';
import '../widgets/shape_handles.dart';
import '../utils/geometry_utils.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import '../painters/grid_system.dart';
import '../painters/ruler_painter.dart';

// Define minimum sizes (match values in shape_handles.dart)
const double minHandleHitboxSize = 32.0;

// Helper to calculate scaled size (updated to match the function in shape_handles.dart)
double _calculateScaledSize(double baseSize, double zoomScale, double minSize) {
  if (zoomScale <= 1.0) return baseSize;
  // Scale down more aggressively with zoom
  return math.max(minSize, baseSize / zoomScale);
}

/// Widget that renders all the vertices and edge handles for a shape
class ShapeHandlesWidget extends StatelessWidget {
  final ShapeData shapeData;
  final bool curveMode;
  final Matrix4 transformMatrix;
  final Function(int, Offset) onVertexDrag;
  final Function(int, Offset) onEdgeDrag;
  final Function(DragEndDetails) onDragEnd;
  final double zoomScale;
  final GridSystem gridSystem;

  const ShapeHandlesWidget({
    super.key,
    required this.shapeData,
    required this.curveMode,
    required this.transformMatrix,
    required this.onVertexDrag,
    required this.onEdgeDrag,
    required this.onDragEnd,
    required this.zoomScale,
    required this.gridSystem,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(children: _buildHandles(context));
  }

  List<Widget> _buildHandles(BuildContext context) {
    final handles = <Widget>[];
    final controller = Get.find<ShapeEditorController>();

    // *** ADD RULER PAINT WIDGET FIRST ***
    handles.add(
      Positioned.fill(
        // Ensure painter can draw anywhere
        child: IgnorePointer(
          ignoring: true, // Make it non-interactive
          child: CustomPaint(
            painter: RulerPainter(
              shapeData: shapeData,
              zoomScale: zoomScale,
              gridSystem: gridSystem,
              canvasTransformMatrix:
                  transformMatrix, // Pass the canvas transform
            ),
            // Let the painter handle hit testing if needed, otherwise ignore
            isComplex: false,
            willChange: false,
          ),
        ),
      ),
    );
    // --- End Ruler Paint ---

    // Determine if this is a group shape
    final isGroupShape =
        shapeData.type == ShapeType.group || shapeData is GroupShapeData;

    // Always enable curve handles for edge handles, except for group shapes
    final shouldShowCurveHandles = !isGroupShape;

    // Calculate scaled hitbox size - using reduced base size of 24.0
    final double baseHitboxSize = 24.0; // Reduced from 30.0
    final double scaledHitboxSize =
        _calculateScaledSize(baseHitboxSize, zoomScale, minHandleHitboxSize);
    final double scaledHitboxOffset = scaledHitboxSize / 2;

    // --- Get the ACCURATE bounding box corners for vertex handles ---
    // Use the GeometryUtils method to get the accurate bounds
    final dynamic accurateBoundsResult =
        GeometryUtils.calculateAccurateBoundingRect(shapeData);

    Rect accurateRect;
    if (accurateBoundsResult is GroupBoundsData) {
      accurateRect = accurateBoundsResult.bounds;
    } else if (accurateBoundsResult is Rect) {
      accurateRect = accurateBoundsResult;
    } else {
      // Fallback to existing bounding rect if calculation fails
      accurateRect = shapeData.boundingRect;
    }
    // --- End Accurate Bounding Box Calculation ---

    // Create a matrix that includes both rotation and translation
    final Matrix4 fullTransformMatrix = Matrix4.identity();

    // Apply translation to account for the shape's position
    fullTransformMatrix.translate(shapeData.center.dx, shapeData.center.dy);

    // Apply rotation around the shape's center
    fullTransformMatrix.rotateZ(shapeData.rotation);

    // Translate back
    fullTransformMatrix.translate(-shapeData.center.dx, -shapeData.center.dy);

    // Define the corners of the ACCURATE bounding box
    final boundingBoxCorners = [
      Offset(accurateRect.left, accurateRect.top), // Top-left
      Offset(accurateRect.right, accurateRect.top), // Top-right
      Offset(accurateRect.right, accurateRect.bottom), // Bottom-right
      Offset(accurateRect.left, accurateRect.bottom) // Bottom-left
    ];

    // First apply the shape's own transformation to the corners
    // Note: Transformation matrix should be applied to the accurate bounding box corners
    // The transformMatrix passed to the widget is for the canvas transform,
    // we need to apply the shape's internal rotation/translation first.
    final Matrix4 shapeTransform = Matrix4.identity()
      ..translate(shapeData.center.dx, shapeData.center.dy)
      ..rotateZ(shapeData.rotation)
      ..translate(-shapeData.center.dx, -shapeData.center.dy);

    final rotatedCorners = boundingBoxCorners
        .map((corner) => MatrixUtils.transformPoint(shapeTransform, corner))
        .toList();

    // Then apply the canvas transformation matrix (passed in via widget.transformMatrix)
    final transformedCorners = rotatedCorners
        .map((corner) => MatrixUtils.transformPoint(transformMatrix, corner))
        .toList();

    // Add vertex handles at the transformed accurate bounding box corners
    for (int i = 0; i < transformedCorners.length; i++) {
      final transformedVertex = transformedCorners[i];

      // Add vertex handle at the corner of the bounding box
      handles.add(
        Positioned(
          left: transformedVertex.dx - scaledHitboxOffset,
          top: transformedVertex.dy - scaledHitboxOffset,
          child: SizedBox(
            width: scaledHitboxSize,
            height: scaledHitboxSize,
            child: GestureDetector(
              onTap: () {
                controller.isHandleInteraction = true;
              },
              behavior: HitTestBehavior.opaque,
              onPanStart: (details) {
                controller.isHandleInteraction = true;
                controller.startHistoryTracking("Adjust Shape Handle");
              },
              onPanUpdate: (d) => onVertexDrag(i, d.globalPosition),
              onPanEnd: onDragEnd,
              onPanCancel: () {
                onDragEnd(DragEndDetails());
              },
              child: Center(
                child: VertexHandle(zoomScale: zoomScale),
              ),
            ),
          ),
        ),
      );
    }

    // Only add edge handles for non-group shapes
    if (!isGroupShape) {
      // Add edge handles on the actual shape edges
      final vertices = shapeData.vertices;
      for (int i = 0; i < vertices.length; i++) {
        final nextIndex = (i + 1) % vertices.length;
        final next = vertices[nextIndex];
        // Apply the shape's own transformation first
        final rotatedVertex =
            MatrixUtils.transformPoint(shapeTransform, vertices[i]);
        final rotatedNext = MatrixUtils.transformPoint(shapeTransform, next);

        // Then apply the canvas transformation
        final transformedVertex =
            MatrixUtils.transformPoint(transformMatrix, rotatedVertex);
        final transformedNext =
            MatrixUtils.transformPoint(transformMatrix, rotatedNext);
        final controlOffset = shapeData.curveControls[i] ?? Offset.zero;
        final edgeVector = next - vertices[i];
        final t = 0.5; // Midpoint parameter for Bezier calculation

        // Calculate the position for the handle
        Offset handlePosition;

        // Calculate handle position - use curve if control offset exists, regardless of mode
        if (controlOffset != Offset.zero) {
          // Calculate the handle position using the same method as ShapePainter._buildCurvedPath
          final edgeMidpoint = Offset(
            (vertices[i].dx + next.dx) / 2,
            (vertices[i].dy + next.dy) / 2,
          );

          final controlPoint = edgeMidpoint + controlOffset;

          // Calculate bezier control points exactly as done in ShapePainter
          final control1 = vertices[i] + edgeVector * 0.25;
          final control2 = next - edgeVector * 0.25;

          // Adjust the control points based on the user's control point
          final adjustedControl1 =
              control1 + (controlPoint - edgeMidpoint) * 0.5;
          final adjustedControl2 =
              control2 + (controlPoint - edgeMidpoint) * 0.5;

          // Calculate the bezier point at t=0.5 (midpoint of the curve)
          final curvePoint = GeometryUtils.calculateBezierPoint(
              vertices[i], adjustedControl1, adjustedControl2, next, t);

          // Apply the shape's own transformation first
          // Apply the shape's internal transform to the calculated curve point
          final rotatedCurvePoint =
              MatrixUtils.transformPoint(shapeTransform, curvePoint);

          // Then apply the canvas transformation
          handlePosition =
              MatrixUtils.transformPoint(transformMatrix, rotatedCurvePoint);
        } else {
          // In normal mode with no curve, use the midpoint of the edge
          handlePosition = (transformedVertex + transformedNext) / 2;
        }

        // Edge handle - IMPROVED to match the vertex handle behavior
        handles.add(
          Positioned(
            left: handlePosition.dx - scaledHitboxOffset,
            top: handlePosition.dy - scaledHitboxOffset,
            child: SizedBox(
              width: scaledHitboxSize,
              height: scaledHitboxSize,
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                // Set isHandleInteraction to true on ANY interaction with the handle
                onTapDown: (_) {
                  controller.isHandleInteraction = true;
                },
                onPanDown: (_) {
                  controller.isHandleInteraction = true;
                },
                onPanStart: (details) {
                  controller.isHandleInteraction = true;
                  controller.startHistoryTracking("Adjust Shape Handle");
                },
                onPanUpdate: (d) => onEdgeDrag(i, d.globalPosition),
                onPanEnd: (details) {
                  // Keep isHandleInteraction true during pan end
                  onDragEnd(details);
                  // Delay resetting the flag until after the event processing is complete
                  Future.delayed(const Duration(milliseconds: 100), () {
                    if (controller.activePointerCount == 0) {
                      controller.isHandleInteraction = false;
                    }
                  });
                },
                onPanCancel: () {
                  onDragEnd(DragEndDetails());
                  // Delay resetting the flag until after the event processing is complete
                  Future.delayed(const Duration(milliseconds: 100), () {
                    if (controller.activePointerCount == 0) {
                      controller.isHandleInteraction = false;
                    }
                  });
                },
                child: Center(
                  child: shouldShowCurveHandles
                      ? CurveHandle(zoomScale: zoomScale)
                      : EdgeHandle(zoomScale: zoomScale),
                ),
              ),
            ),
          ),
        );
      }
    }

    return handles;
  }
}
