import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/constants/grid_constants.dart';
import 'package:xoxknit/app/modules/shape_test/widgets/shape_property_hud.dart';
import 'package:xoxknit/app/modules/new_item/views/new_item_form.dart';
import 'package:xoxknit/app/modules/shape_test/views/shape_editor_view.dart';
import 'package:xoxknit/app/modules/shape_test/views/unified_zones_editor_view.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';
import 'package:xoxknit/app/modules/swatch_calculator/views/swatch_calculator_view.dart';
import '../controllers/new_item_wizard_controller.dart';
import '../controllers/new_item_form_controller.dart';
import './knitting_instructions_summary.dart';
import './interactive_knitting_view.dart';
import './knitting_zone_config_view.dart';

class NewItemWizardView extends GetView<NewItemWizardController> {
  NewItemWizardView({super.key});

  // Add a GlobalKey for the draggable HUD
  final GlobalKey _hudKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    // Ensure ShapeEditorController is ready if we might need it
    // This is usually handled by the WizardController's step logic

    return Obx(() {
      // Determine if we're on the shape editor page (step 2), zones editor (step 3), or interactive knitting (step 6)
      final isShapeEditor = controller.currentStep.value == 2;
      final isZonesEditor = controller.currentStep.value == 3;
      final isInteractiveKnitting = controller.currentStep.value == 6;
      final hideAppBar =
          isShapeEditor || isZonesEditor || isInteractiveKnitting;

      return PopScope(
        // Prevent back navigation via gestures when on shape editor or zones editor page
        canPop: !isShapeEditor && !isZonesEditor,
        child: Scaffold(
          // Hide AppBar on shape editor, zones editor, and interactive knitting pages
          appBar: hideAppBar
              ? null
              : AppBar(
                  title: Text(_getStepTitle()),
                  centerTitle: true,
                ),
          body: SafeArea(
            // Only apply top padding when in shape editor, zones editor, or interactive knitting mode
            top: hideAppBar,
            bottom: false,
            // Use LayoutBuilder to determine screen size for responsive HUD
            child: LayoutBuilder(builder: (context, constraints) {
              return GestureDetector(
                // Wrap Stack with GestureDetector
                behavior: HitTestBehavior.opaque, // Catch taps on empty space
                onTap: () {
                  FocusScope.of(context).unfocus(); // Unfocus on tap outside
                },
                child: Stack(
                  children: [
                    Column(
                      children: [
                        Expanded(
                          child: _buildCurrentStep(),
                        ),
                        _buildNavigationButtons(),
                      ],
                    ),

                    // --- Responsive Shape Property HUD ---
                    if (isShapeEditor)
                      Obx(() {
                        final shapeController = isShapeEditor
                            ? Get.find<ShapeEditorController>()
                            : null;
                        // Determine if screen is large enough for floating HUD
                        final bool isLargeScreen = (constraints.maxWidth >=
                            (shapeController?.hudBreakpoint ?? 600));

                        return isShapeEditor &&
                                shapeController!.selectedIndices.isNotEmpty
                            ? _buildResponsiveShapePropertyHUD(
                                context,
                                shapeController.selectedIndices,
                                constraints,
                                isLargeScreen)
                            : SizedBox.shrink();
                      }),

                    // Loading overlay
                    Obx(() => controller.isLoading.value
                        ? Container(
                            color: Colors.black54,
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          )
                        : const SizedBox.shrink()),
                  ],
                ),
              );
            }),
          ),
        ),
      );
    });
  }

  String _getStepTitle() {
    switch (controller.currentStep.value) {
      case 0:
        return 'newItemWizard_steps_itemDetails'.tr;
      case 1:
        return 'newItemWizard_steps_gaugeCalculator'.tr;
      case 2:
        return 'newItemWizard_steps_shapeEditor'.tr;
      case 3:
        return 'newItemWizard_steps_zonesEditor'.tr;
      case 4:
        return 'newItemWizard_steps_patternSummary'.tr;
      case 5:
        return 'newItemWizard_steps_knittingZoneConfig'.tr;
      case 6:
        return 'newItemWizard_steps_interactiveKnitting'.tr;
      default:
        return 'newItemWizard_steps_createNewItem'.tr;
    }
  }

  Widget _buildCurrentStep() {
    switch (controller.currentStep.value) {
      case 0:
        return NewItemForm();
      case 1:
        return const SwatchInfoScreen();
      case 2:
        return const ShapeEditorView();
      case 3:
        return const UnifiedZonesEditorView(hideAppBar: true);
      case 4:
        return const KnittingInstructionsSummary();
      case 5:
        return const KnittingZoneConfigView();
      case 6:
        return InteractiveKnittingView();
      default:
        return NewItemForm();
    }
  }

  /// Builds the Shape Property HUD, making it responsive and draggable on large screens.
  Widget _buildResponsiveShapePropertyHUD(
      BuildContext context,
      RxList<int> selectedIndices,
      BoxConstraints constraints,
      bool isLargeScreen) {
    final shapeController = Get.find<ShapeEditorController>();

    // Listen to selection, display mode, and potentially position changes
    // Conditions to show the HUD
    if (selectedIndices.isEmpty ||
        shapeController
            .isMultiSelectionMode || // Don't show HUD in multi-selection mode
        (shapeController.propertyDisplayMode.value != PropertyDisplayMode.hud &&
            shapeController.propertyDisplayMode.value !=
                PropertyDisplayMode.both)) {
      return const SizedBox
          .shrink(); // Don't show if no selection, in multi-select mode, or wrong mode
    }

    final selectedIndex = shapeController.selectedIndices.first;
    // Basic bounds check before accessing shapes list
    if (selectedIndex < 0 || selectedIndex >= shapeController.shapes.length) {
      return const SizedBox.shrink();
    }
    final selectedShape = shapeController.shapes[selectedIndex];
    final shapeKey = selectedShape.key;
    final shapeData = shapeController.getShapeState(shapeKey);

    // Ensure we have valid data to build the HUD
    if (shapeKey == null || shapeData == null) {
      return const SizedBox.shrink();
    }

    // Create the core HUD widget instance
    final hudWidget = ShapePropertyHUD(
      key: ValueKey(shapeKey), // Ensures HUD rebuilds if selected shape changes
      controller: shapeController,
      shapeData: shapeData,
      shapeKey: shapeKey,
      screenSize: Size(
          constraints.maxWidth,
          // Use the grid constants for extended height calculation, relevant for the HUD's internal calculations
          GridConstants.getExtendedHeight(constraints.maxHeight)),
    );

    // Initial position calculation
    // Use a safe default size if HUD size hasn't been measured yet
    final defaultHudSize = Size(300, 200); // Reasonable default dimensions
    final currentHudSize = shapeController.hudSize.value.width > 0
        ? shapeController.hudSize.value
        : defaultHudSize;

    // Calculate initial position if not set
    if (shapeController.hudPosition.value == null) {
      final initialX = isLargeScreen ? 20.0 : constraints.maxWidth * 0.1;
      final initialY = isLargeScreen
          ? constraints.maxHeight - currentHudSize.height - 40
          : constraints.maxHeight -
              currentHudSize.height -
              80; // Higher up for small screens

      final safeX =
          initialX.clamp(0.0, constraints.maxWidth - currentHudSize.width);
      final safeY =
          initialY.clamp(0.0, constraints.maxHeight - currentHudSize.height);

      shapeController.hudPosition.value = Offset(safeX, safeY);
    }

    // --- Unified Draggable HUD for both screen sizes ---
    final currentPosition = shapeController.hudPosition.value ??
        Offset(
            isLargeScreen ? 20 : constraints.maxWidth * 0.1,
            constraints.maxHeight -
                currentHudSize.height -
                (isLargeScreen ? 40 : 80)); // Different initial positions

    // --- Deferred Size Measurement Logic ---
    // Measure the HUD size after it's been laid out
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!Get.isRegistered<ShapeEditorController>()) return;
      final shapeCtrl = Get.find<ShapeEditorController>();

      // Check if the widget is still mounted and has a valid context
      if (_hudKey.currentContext != null) {
        try {
          final RenderBox? box =
              _hudKey.currentContext!.findRenderObject() as RenderBox?;

          // Only update if the box has been laid out and has a size
          if (box != null && box.hasSize) {
            final newSize = box.size;
            // Update only if size significantly differs to avoid potential loops
            if ((shapeCtrl.hudSize.value.width - newSize.width).abs() > 0.1 ||
                (shapeCtrl.hudSize.value.height - newSize.height).abs() > 0.1) {
              shapeCtrl.hudSize.value = newSize;

              // If this is the first measurement and position needs adjustment
              if (shapeCtrl.hudPosition.value != null) {
                // Recalculate position to ensure it's within bounds with the actual size
                final currentPos = shapeCtrl.hudPosition.value!;
                final maxX = (constraints.maxWidth - newSize.width)
                    .clamp(0.0, double.infinity);
                final maxY = (constraints.maxHeight - newSize.height)
                    .clamp(0.0, double.infinity);

                if (currentPos.dx > maxX || currentPos.dy > maxY) {
                  final clampedX = currentPos.dx.clamp(2.0, maxX - 2.0);
                  final clampedY = currentPos.dy.clamp(2.0, maxY - 2.0);
                  shapeCtrl.hudPosition.value = Offset(clampedX, clampedY);
                }
              }
            }
          }
        } catch (e) {
          // Silently ignore any layout errors during measurement
          // The HUD will still be functional with the default size
        }
      }
    });

    // --- End Deferred Size Measurement Logic ---

    // Calculate appropriate HUD width based on screen size
    final hudWidth = isLargeScreen
        ? (constraints.maxWidth * 0.4)
            .clamp(320.0, 500.0) // 40% of screen width, min 320, max 500
        : (constraints.maxWidth * 0.8)
            .clamp(280.0, 400.0); // 80% of screen width, min 280, max 400

    return Positioned(
      left: currentPosition.dx,
      top: currentPosition.dy,
      child: GestureDetector(
        // Use GestureDetector for smoother dragging without jumping
        onPanUpdate: (details) {
          final shapeCtrl =
              Get.find<ShapeEditorController>(); // Find controller
          final currentHudPosition = shapeCtrl.hudPosition.value ??
              Offset(
                  isLargeScreen ? 20 : constraints.maxWidth * 0.1,
                  constraints.maxHeight -
                      currentHudSize.height -
                      (isLargeScreen ? 40 : 80));

          final newPosition = Offset(currentHudPosition.dx + details.delta.dx,
              currentHudPosition.dy + details.delta.dy);

          // Use the size from the controller (which should be updated by the measurement logic)
          Size hudSize = shapeCtrl.hudSize.value.width > 0
              ? shapeCtrl.hudSize.value
              : defaultHudSize;

          // --- Update size if context is available during drag ---
          // This handles cases where content might change size *during* a drag, though less common
          if (_hudKey.currentContext != null) {
            try {
              final RenderBox? box =
                  _hudKey.currentContext!.findRenderObject() as RenderBox?;
              if (box != null &&
                  box.hasSize &&
                  ((hudSize.width - box.size.width).abs() > 0.1 ||
                      (hudSize.height - box.size.height).abs() > 0.1)) {
                hudSize = box.size;
                shapeCtrl.hudSize.value = hudSize; // Update controller
              }
            } catch (e) {
              // Ignore layout errors during drag
            }
          }
          // --- End Update size ---

          final margin = 2.0;
          // Clamp based on the controller's hudSize
          final maxX = (constraints.maxWidth - hudSize.width)
              .clamp(0.0, double.infinity);
          final maxY = (constraints.maxHeight - hudSize.height)
              .clamp(0.0, double.infinity);

          final clampedX = newPosition.dx.clamp(margin, maxX - margin);
          final clampedY = newPosition.dy.clamp(margin, maxY - margin);

          shapeCtrl.hudPosition.value = Offset(clampedX, clampedY);
        },
        // Wrap the HUD in a SizedBox to provide explicit width constraints
        child: SizedBox(
          width: hudWidth,
          child: Container(key: _hudKey, child: hudWidget),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    final formController = Get.find<NewItemFormController>();

    // Define a common button style for both buttons, including disabled state
    final buttonStyle = ElevatedButton.styleFrom(
      backgroundColor: Get.theme.colorScheme.primary,
      foregroundColor: Get.theme.colorScheme.onPrimary,
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 12,
      ),
      fixedSize: const Size(160, 48), // Fixed size for consistency
      minimumSize: const Size(160, 48),
      maximumSize: const Size(160, 48),
      shape: RoundedRectangleBorder(
        borderRadius:
            BorderRadius.circular(8.0), // Same border radius for both buttons
      ),
      // Add disabled styles for visual feedback
      disabledBackgroundColor:
          Get.theme.colorScheme.primary.withValues(alpha: 0.5),
      disabledForegroundColor:
          Get.theme.colorScheme.onPrimary.withValues(alpha: 0.7),
    );

    final outlineButtonStyle = OutlinedButton.styleFrom(
      foregroundColor: Get.theme.colorScheme.primary,
      side: BorderSide(color: Get.theme.colorScheme.primary),
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
        vertical: 12,
      ),
      fixedSize: const Size(160, 48), // Fixed size to match primary button
      minimumSize: const Size(160, 48),
      maximumSize: const Size(160, 48),
      shape: RoundedRectangleBorder(
        borderRadius:
            BorderRadius.circular(8.0), // Same border radius for both buttons
      ),
    );

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Get.theme.colorScheme.shadow.withValues(alpha: 0.5),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button for all steps
          Obx(() => SizedBox(
                width: 160,
                height: 48,
                child: OutlinedButton.icon(
                  onPressed: controller.currentStep.value == 0
                      ? () => Get.rootDelegate.popRoute()
                      : controller.previousStep,
                  icon: const Icon(Icons.arrow_back),
                  label: Text(
                    'newItemWizard_navigation_previous'.tr,
                    overflow: TextOverflow.ellipsis,
                  ),
                  style: outlineButtonStyle,
                ),
              )),
          Obx(() {
            // Logic for the right button:
            // - If we're at the last step (6-Interactive Knitting), show "Save & knit"
            // - If we're at step 5 (Knitting Zone Config), show "Start Knitting"
            // - Otherwise show "Next" or "Get Instructions" depending on the step
            if (controller.currentStep.value == 6) {
              // Final step - show a placeholder with exact same dimensions
              return const SizedBox(width: 160, height: 48);
            } else if (controller.currentStep.value == 5) {
              // Knitting Zone Config step - show next button to start knitting
              return SizedBox(
                width: 160,
                height: 48,
                child: ElevatedButton.icon(
                  onPressed: () => controller.nextStep(),
                  icon: const Icon(Icons.arrow_forward),
                  label: Text('newItemWizard_navigation_next'.tr,
                      overflow: TextOverflow.ellipsis),
                  style: buttonStyle,
                ),
              );
            } else {
              // Other steps - show next button (potentially disabled on step 2)

              // Determine if the button should be disabled (only on step 2 if no shapes)
              bool isNextButtonDisabled = false;
              String tooltipMessage = ''; // Default empty tooltip
              if (controller.currentStep.value == 2) {
                if (Get.isRegistered<ShapeEditorController>()) {
                  final shapeEditorController =
                      Get.find<ShapeEditorController>();
                  // Reactively check if shapes list is empty
                  isNextButtonDisabled = shapeEditorController.shapes.isEmpty;
                  if (isNextButtonDisabled) {
                    tooltipMessage = 'newItemWizard_errors_addShapeRequired'.tr;
                  }
                } else {
                  // If controller not registered, assume no shapes (disable)
                  isNextButtonDisabled = true;
                  tooltipMessage = 'newItemWizard_errors_shapeEditorLoading'.tr;
                }
              }

              // Use a SizedBox to maintain layout consistency
              return SizedBox(
                width: 160,
                height: 48,
                // Wrap with Tooltip to explain disabled state
                child: Tooltip(
                  message: isNextButtonDisabled
                      ? tooltipMessage
                      : '', // Only show message if disabled
                  child: ElevatedButton.icon(
                    onPressed: isNextButtonDisabled
                        ? null // Disable the button
                        : () async {
                            // Normal action
                            // Original logic for proceeding
                            if (controller.currentStep.value == 0) {
                              formController.submitForm();
                            } else if (controller.currentStep.value == 2) {
                              // When moving from shape editor to summary
                              try {
                                if (Get.isRegistered<ShapeEditorController>()) {
                                  // Generate instructions before moving to summary
                                  // Show loading indicator
                                  Get.dialog(
                                    Center(
                                      child: Card(
                                        child: Padding(
                                          padding: EdgeInsets.all(24.0),
                                          child: Column(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              CircularProgressIndicator(),
                                              SizedBox(height: 16),
                                              Text(
                                                  'newItemWizard_generatingInstructions'
                                                      .tr),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    barrierDismissible: false,
                                  );

                                  // Generate instructions using our new manager
                                  await Future.delayed(const Duration(
                                      milliseconds:
                                          100)); // Give UI time to show dialog

                                  // Generate instructions but don't save them to wizard state
                                  await controller
                                      .generateKnittingInstructions();

                                  // Close the dialog
                                  Get.rootDelegate.popRoute();

                                  // Move to next step
                                  controller.nextStep();
                                } else {
                                  Get.snackbar(
                                    'newItemWizard_errors_title'.tr,
                                    'newItemWizard_errors_shapeEditorNotFound'
                                        .tr,
                                    snackPosition: SnackPosition.BOTTOM,
                                    backgroundColor:
                                        Get.theme.colorScheme.errorContainer,
                                  );
                                }
                              } catch (e) {
                                // Close dialog if open
                                if (Get.isDialogOpen ?? false) {
                                  Get.back();
                                }

                                Get.snackbar(
                                  'newItemWizard_errors_title'.tr,
                                  '${'errors_generationFailed'.tr}: ${e.toString()}',
                                  snackPosition: SnackPosition.BOTTOM,
                                  backgroundColor:
                                      Get.theme.colorScheme.errorContainer,
                                );
                              }
                            } else {
                              controller.nextStep();
                            }
                          },
                    icon: const Icon(
                        Icons.arrow_forward), // Correctly placed icon
                    label: Text(
                      // Correctly placed label
                      'newItemWizard_navigation_next'.tr,
                      overflow: TextOverflow.ellipsis,
                    ),
                    style:
                        buttonStyle, // Apply the style with disabled state handling
                  ),
                ),
              );
            }
          }),
        ],
      ),
    );
  }
}
