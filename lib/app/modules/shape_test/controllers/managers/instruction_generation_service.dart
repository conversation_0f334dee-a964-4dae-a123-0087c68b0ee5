import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/new_item/controllers/new_item_wizard_controller.dart';
import 'package:xoxknit/app/modules/new_item/utils/knitting_utils.dart';
import 'package:xoxknit/app/modules/shape_test/utils/knitting_instructions_generator_new.dart';
import '../../models/shape_data.dart';
import '../../utils/knitting_pattern_generator.dart';
import '../shape_editor_controller.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// Service for generating knitting instructions from shapes
class InstructionGenerationService {
  final ShapeEditorController _controller;

  InstructionGenerationService(this._controller);

  /// Generate knitting instructions from the current shapes
  Future<List<List<bool>>> generateInstructions() async {
    try {
      // Get all shape data from the controller's shapes
      final List<ShapeData> shapes = [];

      // Collect shape data from all shapes using the controller's getShapeState method
      for (final shape in _controller.shapes) {
        if (shape.key != null) {
          final shapeData = _controller.getShapeState(shape.key);
          if (shapeData != null) {
            shapes.add(shapeData);
          }
        }
      }

      final NewItemWizardController newItemWizardController =
          NewItemWizardController.to;

      final needleCount =
          newItemWizardController.newItem.value.knittingMachine!.needlesCount;
      final stitchesPerCm = newItemWizardController.newItem.value.stitchesPerCm;
      final rowsPerCm = newItemWizardController.newItem.value.rowsPerCm;

      // Calculate aspect ratio (stitch height / stitch width)
      double aspectRatio = 1.5; // Default value
      if (rowsPerCm != null &&
          rowsPerCm > 0 &&
          stitchesPerCm != null &&
          stitchesPerCm > 0) {
        aspectRatio = stitchesPerCm / rowsPerCm;
      }

      // Get grid dimensions from the grid system
      final gridSystem = _controller.gridSystem;

      // Validate gauge settings
      if (stitchesPerCm == null ||
          stitchesPerCm <= 0 ||
          rowsPerCm == null ||
          rowsPerCm <= 0) {
        Get.snackbar(
          'Warning',
          'Please set your knitting gauge in the settings first',
          backgroundColor: Colors.orange,
        );
        return [];
      }

      // Force update of needle mapping to ensure accurate calculations
      gridSystem.updateViewport(gridSystem.zoomLevel, gridSystem.panOffset,
          updateNeedleMapping: true);

      // Calculate combined bounds of all shapes
      Rect? combinedBounds;
      for (final shape in shapes) {
        // Get shape bounds
        final bounds = getShapeBounds(shape);

        if (combinedBounds == null) {
          combinedBounds = bounds;
        } else {
          // Expand to include this shape
          combinedBounds = Rect.fromLTRB(
            math.min(combinedBounds.left, bounds.left),
            math.min(combinedBounds.top, bounds.top),
            math.max(combinedBounds.right, bounds.right),
            math.max(combinedBounds.bottom, bounds.bottom),
          );
        }
      }

      // Generate the stitch pattern using the converter
      // Ensure a valid aspect ratio
      final validAspectRatio =
          (aspectRatio <= 0 || aspectRatio.isNaN || aspectRatio.isInfinite)
              ? 1.5
              : aspectRatio;

      // --- Calculate cellWidth based on screen width and needle count ---
      final screenWidth = Get.width;
      final cellWidthForGeneration = (needleCount > 0 && screenWidth > 0)
          ? screenWidth / needleCount
          : 2.0; // Default fallback if needleCount or screenWidth is invalid
      debugPrint(
          'Using screenWidth / needleCount for generation cellWidth: $cellWidthForGeneration (Screen: $screenWidth, Needles: $needleCount)');
      // -----------------------------------------------------------------

      // Generate the instructions
      final newInstructions = convertShapesToStitchPattern(
        shapes: shapes,
        columns: needleCount,
        cellWidth: cellWidthForGeneration,
        gridSystem: gridSystem,
        boundingBox: combinedBounds,
        aspectRatio: validAspectRatio,
      );

      return newInstructions;
    } catch (e, stackTrace) {
      debugPrint('Error generating knitting instructions: $e');
      debugPrint('Stack trace: $stackTrace');
      return [];
    }
  }

  /// Get the bounding rectangle for a shape, considering curves and rotation.
  Rect getShapeBounds(ShapeData shape) {
    if (shape.vertices.isEmpty) {
      return Rect.zero;
    }

    // Use the path-based approach to get accurate bounds including curves
    final path = createShapePath(shape);
    return path.getBounds();
  }

  /// Create a path for a single shape, properly handling rotation and curves
  ui.Path createShapePath(ShapeData shape) {
    // Handle grouped shapes by recursively creating paths for each child shape
    // Note: This requires GroupShapeData definition or a dynamic check
    // Assuming ShapeData has a 'type' and potentially 'childShapes' for groups
    // if (shape.type == ShapeType.group && shape is GroupShapeData) { // Example if GroupShapeData is available
    // Using dynamic check as GroupShapeData might not be directly imported here
    if (shape.runtimeType.toString().contains('GroupShapeData')) {
      try {
        final dynamic groupShape = shape;
        if (groupShape.childShapes != null) {
          final childShapes = groupShape.childShapes as List<ShapeData>;

          if (childShapes.isEmpty) return ui.Path();

          // Create a path for each child shape and combine them
          ui.Path combinedPath = ui.Path();
          for (final childShape in childShapes) {
            final childPath = createShapePath(childShape);
            combinedPath.addPath(childPath, Offset.zero);
          }
          return combinedPath;
        }
      } catch (e) {
        debugPrint("Error processing group shape in createShapePath: $e");
        // Fallback to using group's own vertices if child access fails
      }
    }

    final path = ui.Path();

    // Skip shapes with no vertices
    if (shape.vertices.isEmpty) return path;

    // Get all vertices, applying rotation if needed
    final vertices = shape.rotation != 0
        ? shape.vertices
            .map((v) => rotatePoint(v, shape.center, shape.rotation))
            .toList()
        : shape.vertices;

    if (vertices.isEmpty) return path; // Check again after potential rotation

    // Move to the first vertex
    path.moveTo(vertices[0].dx, vertices[0].dy);

    // Draw each edge of the shape
    for (int i = 0; i < vertices.length; i++) {
      final nextIndex = (i + 1) % vertices.length;
      final currentVertex = vertices[i];
      final nextVertex = vertices[nextIndex];

      // Check if this edge has a curve control point
      if (shape.curveControls.containsKey(i)) {
        // Calculate the midpoint of the *rotated* edge
        final midpoint = Offset((currentVertex.dx + nextVertex.dx) / 2,
            (currentVertex.dy + nextVertex.dy) / 2);

        // Get the control point offset (relative to the midpoint)
        final controlOffset = shape.curveControls[i]!;

        // Calculate the absolute control point position
        final controlPoint =
            midpoint.translate(controlOffset.dx, controlOffset.dy);

        // NOTE: Unlike the version in TransformableShape, the vertices here are ALREADY rotated.
        // The control point calculated from the rotated midpoint also needs to be considered in the rotated space.
        // No further rotation needed for the control point itself IF vertices were pre-rotated.
        final effectiveControlPoint = controlPoint;

        // Add a quadratic bezier curve to the path
        path.quadraticBezierTo(effectiveControlPoint.dx,
            effectiveControlPoint.dy, nextVertex.dx, nextVertex.dy);
      } else {
        // Add a straight line to the next vertex
        path.lineTo(nextVertex.dx, nextVertex.dy);
      }
    }

    // Close the path
    path.close();
    return path;
  }

  /// Helper function to rotate a point around a center
  Offset rotatePoint(Offset point, Offset center, double angle) {
    // Calculate position relative to center
    final dx = point.dx - center.dx;
    final dy = point.dy - center.dy;

    // Apply rotation
    final cosTheta = math.cos(angle);
    final sinTheta = math.sin(angle);

    final rotatedDx = dx * cosTheta - dy * sinTheta;
    final rotatedDy = dx * sinTheta + dy * cosTheta;

    // Return point in original coordinate system
    return Offset(center.dx + rotatedDx, center.dy + rotatedDy);
  }

  /// Get a text representation of the current knitting pattern
  String getTextPattern(List<List<bool>> instructions, bool includeRowNumbers) {
    return KnittingPatternGenerator.generateTextPattern(
      instructions: instructions,
      startFromBottom: false, // Always use top-down
      includeRowNumbers: includeRowNumbers,
    );
  }

  /// Get a structured representation of the current knitting pattern
  List<List<String>> getStructuredPattern(List<List<bool>> instructions) {
    return KnittingPatternGenerator.generateStructuredPattern(
      instructions: instructions,
      startFromBottom: false, // Always use top-down
    );
  }

  /// Calculate pattern statistics
  Map<String, dynamic> calculatePatternStatistics(
      List<List<bool>> instructions) {
    return KnittingPatternGenerator.calculatePatternStatistics(instructions);
  }
}
