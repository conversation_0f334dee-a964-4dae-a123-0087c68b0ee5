import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/shape_editor_controller.dart';
import '../constants/grid_constants.dart';

// Convert to StatefulWidget
class ShapeContextMenu extends StatefulWidget {
  final ShapeEditorController controller;
  final VoidCallback onDismiss;

  // Define a breakpoint for switching layouts
  static const double horizontalLayoutBreakpoint = 600.0;

  const ShapeContextMenu({
    super.key,
    required this.controller,
    required this.onDismiss,
  });

  @override
  State<ShapeContextMenu> createState() => _ShapeContextMenuState();
}

class _ShapeContextMenuState extends State<ShapeContextMenu>
    with SingleTickerProviderStateMixin {
  // Add state variables
  int _currentPageIndex = 0; // 0 for primary, 1 for secondary
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200), // Adjust duration as needed
    );

    // Configure slide animation (adjust begin/end offsets as needed)
    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0), // Slide in from right
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    // Optional: Add listener if needed for animation status
    // _animationController.addStatusListener((status) { ... });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Function to toggle pages
  void _togglePage(int newPageIndex) {
    if (_currentPageIndex != newPageIndex) {
      setState(() {
        _currentPageIndex = newPageIndex;
      });
      if (newPageIndex == 1) {
        // Going to secondary page
        _slideAnimation = Tween<Offset>(
          begin: const Offset(1.0, 0.0), // Slide in from right
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ));
        _animationController.forward(from: 0.0);
      } else {
        // Going back to primary page
        _slideAnimation = Tween<Offset>(
          begin: const Offset(-1.0, 0.0), // Slide in from left
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: Curves.easeInOut,
        ));
        _animationController.forward(
            from: 0.0); // Use forward for consistency if needed, or reverse
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use the right key for position calculation

    // Add post-frame callback to calculate/adjust position after layout
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Ensure the menu is still visible and the key has context before calculating
      if (widget.controller.isContextMenuVisible.value &&
          widget.controller.contextMenuKey.currentContext != null &&
          context.mounted) {
        widget.controller.calculateAndSetMenuPosition(context);
      }
    });

    final screenWidth = MediaQuery.of(context).size.width;
    final bool useHorizontalLayout =
        screenWidth < ShapeContextMenu.horizontalLayoutBreakpoint;

    // Use Obx for the Positioned widget itself to react to position changes
    return Obx(() {
      // Get the calculated position, provide a default if null (though shouldn't happen if visible)
      final Offset currentPosition =
          widget.controller.calculatedContextMenuPosition.value ?? Offset.zero;

      // Return SizedBox if not visible or position is not calculated yet
      if (!widget.controller.isContextMenuVisible.value ||
          widget.controller.calculatedContextMenuPosition.value == null) {
        return const SizedBox.shrink();
      }

      return Stack(
        children: [
          // Overlay for capturing taps outside the menu
          Positioned.fill(
            child: GestureDetector(
              onTap: widget.onDismiss,
              behavior: HitTestBehavior.translucent,
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // The actual menu - Positioned using calculated value
          Positioned(
            left: currentPosition.dx, // Use calculated position
            top: currentPosition.dy, // Use calculated position
            child: GestureDetector(
              onTap: () {}, // Prevent taps on menu background closing it
              behavior: HitTestBehavior.opaque,
              // Wrap with AnimatedSize for smooth resizing
              child: AnimatedSize(
                key: widget.controller.contextMenuKey,
                duration: const Duration(
                    milliseconds: 150), // Match animation roughly
                curve: Curves.easeInOut,
                child: Material(
                  elevation: 5,
                  // Use slightly different styling for horizontal layout?
                  borderRadius:
                      BorderRadius.circular(useHorizontalLayout ? 10 : 8),
                  clipBehavior:
                      Clip.antiAlias, // Ensures content respects border radius
                  color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
                  // Obx moved inside to rebuild menu options based on controller state
                  child: Obx(() {
                    final hasSelection =
                        widget.controller.selectedIndices.isNotEmpty;
                    final isMultiSelection =
                        widget.controller.selectedIndices.length > 1;
                    final isGroupSelected =
                        hasSelection && widget.controller.isGroupSelected();

                    // Choose layout based on screen width
                    return useHorizontalLayout
                        ? _buildHorizontalMenu(context, hasSelection,
                            isMultiSelection, isGroupSelected)
                        : _buildVerticalMenu(context, hasSelection,
                            isMultiSelection, isGroupSelected);
                  }),
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  // Builds the traditional vertical menu
  Widget _buildVerticalMenu(BuildContext context, bool hasSelection,
      bool isMultiSelection, bool isGroupSelected) {
    // Build widgets for both pages
    final page0Items = _buildMenuPage(
        0, context, hasSelection, isMultiSelection, isGroupSelected, false);
    final page1Items = _buildMenuPage(
        1, context, hasSelection, isMultiSelection, isGroupSelected, false);

    // Create Column widgets outside AnimatedSwitcher
    final page0Column = Column(
      key: const ValueKey(0), // Key the content itself
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: page0Items,
    );
    final page1Column = Column(
      key: const ValueKey(1), // Key the content itself
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: page1Items,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: AnimatedSwitcher(
        duration: const Duration(milliseconds: 200),
        transitionBuilder: (Widget child, Animation<double> animation) {
          // Determine direction based on the *child's key* (which is 0 or 1)
          final pageKey = child.key as ValueKey<int>;
          final bool isEnteringPage1 = pageKey.value == 1;

          // Adjust the slide direction based on which page is entering
          // Use a local animation variable for the transition builder
          final slideAnimation = Tween<Offset>(
            begin: isEnteringPage1
                ? const Offset(1.0, 0.0)
                : const Offset(
                    -1.0, 0.0), // Slide from right for page 1, left for page 0
            end: Offset.zero,
          ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut)); // Use the provided animation

          return SlideTransition(
            position: slideAnimation, // Use the locally defined animation
            child: child,
          );
        },
        // Pass the pre-built Column based on state
        child: _currentPageIndex == 0 ? page0Column : page1Column,
      ),
    );
  }

  // Builds the new horizontal menu
  Widget _buildHorizontalMenu(BuildContext context, bool hasSelection,
      bool isMultiSelection, bool isGroupSelected) {
    // Build widgets for both pages
    final page0Items = _buildMenuPage(
        0, context, hasSelection, isMultiSelection, isGroupSelected, true);
    final page1Items = _buildMenuPage(
        1, context, hasSelection, isMultiSelection, isGroupSelected, true);

    // Create Row widgets outside AnimatedSwitcher
    final page0Row = Row(
      key: const ValueKey(0), // Key the content itself
      mainAxisSize: MainAxisSize.min,
      children: page0Items,
    );
    final page1Row = Row(
      key: const ValueKey(1), // Key the content itself
      mainAxisSize: MainAxisSize.min,
      children: page1Items,
    );

    // Use SingleChildScrollView to prevent overflow issues if items are too wide
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: 0.0), // No horizontal padding
        child: AnimatedSwitcher(
          duration: const Duration(milliseconds: 200),
          transitionBuilder: (Widget child, Animation<double> animation) {
            // Determine direction based on the *child's key* (which is 0 or 1)
            final pageKey = child.key as ValueKey<int>;
            final bool isEnteringPage1 = pageKey.value == 1;

            // Use a local animation variable for the transition builder
            final slideAnimation = Tween<Offset>(
              begin: isEnteringPage1
                  ? const Offset(1.0, 0.0)
                  : const Offset(-1.0,
                      0.0), // Slide from right for page 1, left for page 0
              end: Offset.zero,
            ).animate(CurvedAnimation(
                parent: animation,
                curve: Curves.easeInOut)); // Use the provided animation

            return SlideTransition(
              position: slideAnimation,
              child: child,
            );
          },
          // Pass the pre-built Row based on state
          child: _currentPageIndex == 0 ? page0Row : page1Row,
        ),
      ),
    );
  }

  // New method to build options for a specific page
  List<Widget> _buildMenuPage(
      int pageIndex,
      BuildContext context,
      bool hasSelection,
      bool isMultiSelection,
      bool isGroupSelected,
      bool isHorizontal) {
    final textColor = Get.isDarkMode ? Colors.white : Colors.black87;
    final dividerColor = Get.isDarkMode ? Colors.grey[700] : Colors.grey[300];

    // Define all possible actions (reuse definitions from previous step)
    final actions = <String, Widget Function()>{
      'cut': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_cut'.tr,
            icon: Icons.cut,
            onTap: () {
              widget.controller.cutSelectedShapes();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'copy': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_copy'.tr,
            icon: Icons.copy,
            onTap: () {
              widget.controller.copySelectedShapes();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'paste': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_paste'.tr,
            icon: Icons.paste,
            onTap: () {
              // --- FIX: Convert screen position to grid position for paste ---
              final Offset? screenPosition =
                  widget.controller.initialContextMenuPosition.value;
              if (screenPosition != null) {
                // --- Use FULL GRID SIZE for accurate conversion ---
                final double screenWidth = MediaQuery.of(context).size.width;
                final double baseScreenHeight =
                    MediaQuery.of(context).size.height;
                final double extendedGridHeight =
                    GridConstants.getExtendedHeight(baseScreenHeight);
                final Size fullGridSize = Size(screenWidth, extendedGridHeight);
                // -------------------------------------------------

                final Offset gridPosition = widget.controller.gridSystem
                    .screenToGridPoint(screenPosition, fullGridSize);
                widget.controller.pasteShapes(gridPosition);
              } else {
                // Fallback if position is somehow null (shouldn't happen if menu is visible)
                widget.controller.pasteShapes();
              }
              // -------------------------------------------------------------
              widget.onDismiss();
            },
            textColor: textColor,
            enabled: widget.controller.canPaste(),
            isHorizontal: isHorizontal,
          ),
      'duplicate': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_duplicate'.tr,
            icon: Icons.content_copy,
            onTap: () {
              widget.controller.duplicateSelectedShapes();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'delete': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_delete'.tr,
            icon: Icons.delete,
            onTap: () {
              widget.controller.deleteSelectedShapes();
              widget.onDismiss();
            },
            textColor: isHorizontal ? Colors.red : textColor,
            isHorizontal: isHorizontal,
          ),
      'group': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_group'.tr,
            icon: Icons.group_work,
            onTap: () {
              widget.controller.groupSelectedShapes();
              widget.onDismiss();
            },
            textColor: textColor,
            // Use Obx to reactively check if grouping is possible right now
            enabled: widget.controller.canGroupSelectedShapes(),
            isHorizontal: isHorizontal,
          ),
      'ungroup': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_ungroup'.tr,
            icon: Icons.layers_clear,
            onTap: () {
              widget.controller.ungroupSelectedShape();
              widget.onDismiss();
            },
            textColor: textColor,
            enabled: widget.controller.canUngroupSelectedShape(),
            isHorizontal: isHorizontal,
          ),
      'select_objects': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_selectObjects'.tr,
            icon: Icons.select_all,
            onTap: () {
              widget.controller.toggleMultiSelectionMode();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
          ),
      'flip_horizontal': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_flipHorizontal'.tr,
            icon: Icons.flip,
            onTap: () {
              widget.controller.flipSelectedShapeHorizontally();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
            isNavigation: false,
          ),
      'flip_vertical': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_flipVertical'.tr,
            icon: Icons.flip,
            onTap: () {
              widget.controller.flipSelectedShapeVertically();
              widget.onDismiss();
            },
            textColor: textColor,
            isHorizontal: isHorizontal,
            isNavigation: false,
            iconRotation: 1.5708, // 90 degrees in radians
          ),
      'more': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_more'.tr,
            icon: Icons.arrow_forward_ios,
            onTap: () => _togglePage(1),
            textColor: textColor,
            isHorizontal: isHorizontal,
            isNavigation: true,
          ),
      'back': () => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_back'.tr,
            icon: Icons.arrow_back_ios,
            onTap: () => _togglePage(0),
            textColor: textColor,
            isHorizontal: isHorizontal,
            isNavigation: true,
          ),
    };

    List<Widget Function()> pageActions = [];

    // Always check the CURRENT selection state for more reliable menu building
    final currentHasSelection = widget.controller.selectedIndices.isNotEmpty;
    final currentIsMultiSelection =
        widget.controller.selectedIndices.length > 1;
    final currentIsGroupSelected =
        currentHasSelection && widget.controller.isGroupSelected();
    final currentCanGroup = widget.controller.canGroupSelectedShapes();
    final currentCanUngroup = widget.controller.canUngroupSelectedShape();

    if (pageIndex == 0) {
      // --- Page 0: Primary Actions ---
      if (!currentHasSelection) {
        // No selection: Show Select Objects and Paste (if available)
        if (widget.controller.canPaste()) {
          pageActions.add(actions['paste']!);
        }
        // Only show Select Objects if there are at least 2 shapes
        if (widget.controller.shapes.length > 1) {
          pageActions.add(actions['select_objects']!);
        }
        // No 'More' button when no selection is present
      } else {
        // Add Paste if available
        if (widget.controller.canPaste()) {
          pageActions.add(actions['paste']!);
        }
        // Selection exists: Show primary actions
        pageActions.add(actions['cut']!);
        pageActions.add(actions['copy']!);
        pageActions.add(actions['duplicate']!);
        pageActions.add(actions['delete']!);

        // IMPORTANT: Add Group to the primary page if we have multiple selections
        // This ensures it's always visible when needed
        if (currentIsMultiSelection && currentCanGroup) {
          pageActions.add(actions['group']!);
        }

        // Show 'More' button if secondary actions are available
        bool hasSecondaryActions = (currentIsGroupSelected &&
                currentCanUngroup) ||
            widget.controller.shapes.length >
                1; // Only consider Select Objects if we have more than 1 shape

        if (hasSecondaryActions) {
          pageActions.add(actions['more']!); // Add 'More' button
        }
      }
    } else {
      // pageIndex == 1
      // --- Page 1: Secondary Actions ---
      pageActions.add(actions['back']!); // Always add back button first

      // Only show Select Objects if there are at least 2 shapes
      if (widget.controller.shapes.length > 1) {
        pageActions.add(actions['select_objects']!);
      }

      // Add flipping options when shapes are selected
      if (currentHasSelection) {
        // Add flip horizontal option
        pageActions.add(actions['flip_horizontal']!);

        // Add flip vertical option
        pageActions.add(actions['flip_vertical']!);
      }

      // Conditionally show Ungroup option
      if (currentHasSelection && currentIsGroupSelected && currentCanUngroup) {
        pageActions.add(actions['ungroup']!);
      }
    }

    // Build the final list of widgets for this page, inserting dividers
    List<Widget> widgets = [];
    for (int i = 0; i < pageActions.length; i++) {
      widgets.add(pageActions[i]()); // Call the function to build the widget

      // Add appropriate dividers based on layout and position
      if (i < pageActions.length - 1) {
        if (isHorizontal) {
          // Horizontal layout: Add vertical dividers
          // Don't add divider next to nav icons if horizontal
          bool isCurrentNav = pageActions[i] == actions['more'] ||
              pageActions[i] == actions['back'];
          bool isNextNav = (i + 1 < pageActions.length) &&
              (pageActions[i + 1] == actions['more'] ||
                  pageActions[i + 1] == actions['back']);
          if (!isCurrentNav && !isNextNav) {
            widgets.add(_buildVerticalDivider(dividerColor ?? Colors.grey));
          }
        } else {
          // Vertical layout: Add horizontal dividers
          // Don't add divider after back/more buttons
          bool isNavigationButton = pageActions[i] == actions['more'] ||
              pageActions[i] == actions['back'];

          // Only add dividers in the second page after the back button (index 0)
          // or between regular menu items
          if (pageIndex == 1 && ((i == 0 && isNavigationButton) || i > 0)) {
            widgets.add(_buildHorizontalDivider(dividerColor ?? Colors.grey));
          } else if (pageIndex == 0 && i < pageActions.length - 1) {
            // For first page, add dividers between all items
            widgets.add(_buildHorizontalDivider(dividerColor ?? Colors.grey));
          }
        }
      }
    }
    return widgets;
  }

  // Updated item builder to handle navigation icons
  Widget _buildMenuItem({
    required String label,
    IconData? icon, // Icon is optional now
    required VoidCallback onTap,
    required Color textColor,
    bool enabled = true,
    required bool isHorizontal,
    bool isNavigation = false, // New flag
    double? iconRotation, // New optional rotation
  }) {
    final Color currentTextColor =
        enabled ? textColor : textColor.withOpacity(0.4);
    final Color? specificColor =
        (label == 'Delete' && isHorizontal) ? Colors.red : null;
    final Color effectiveTextColor = specificColor ?? currentTextColor;

    final double verticalPadding = isHorizontal ? 8.0 : 10.0;
    final double horizontalPadding =
        isNavigation && isHorizontal ? 8.0 : 14.0; // Less padding for nav icons

    Widget child;
    if (isHorizontal) {
      // Horizontal: Show only icon for navigation, text for others
      child = isNavigation
          ? icon != null
              ? iconRotation != null
                  ? Transform.rotate(
                      angle: iconRotation,
                      child: Icon(icon, size: 20, color: effectiveTextColor))
                  : Icon(icon, size: 20, color: effectiveTextColor)
              : Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: effectiveTextColor,
                  ),
                )
          : Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: effectiveTextColor,
              ),
              overflow: TextOverflow.ellipsis, // Prevent text overflow
            );
    } else {
      // Vertical: Show icon and text
      child = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            iconRotation != null
                ? Transform.rotate(
                    angle: iconRotation,
                    child: Icon(icon, size: 20, color: effectiveTextColor),
                  )
                : Icon(icon, size: 20, color: effectiveTextColor),
            const SizedBox(width: 12),
          ],
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: effectiveTextColor,
            ),
          ),
        ],
      );
    }

    return TextButton(
      onPressed: enabled ? onTap : null,
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding, vertical: verticalPadding),
        minimumSize: Size(0, 36), // Allow button to shrink
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        foregroundColor: effectiveTextColor,
        backgroundColor: Colors.transparent,
        disabledForegroundColor: effectiveTextColor.withOpacity(0.5),
        splashFactory:
            isHorizontal ? NoSplash.splashFactory : InkSplash.splashFactory,
      ),
      child: child,
    );
  }

  // Divider for Vertical layout (remains the same)
  Widget _buildHorizontalDivider(Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Container(
        height: 1,
        color: color,
      ),
    );
  }

  // New Divider for Horizontal layout
  Widget _buildVerticalDivider(Color color) {
    return Container(
      height: 20, // Adjust height to fit button text roughly
      width: 1,
      color: color,
      margin: const EdgeInsets.symmetric(
          vertical: 8.0), // Align with button padding
    );
  }
}
