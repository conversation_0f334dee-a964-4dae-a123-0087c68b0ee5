import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:xoxknit/app/data/models/knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/new_item_model.dart';
import 'package:xoxknit/app/data/models/user_knitting_machine_model.dart';
import 'package:xoxknit/app/data/models/user_model.dart';
import 'package:xoxknit/app/modules/user_machines/views/components/machine_dialog.dart';
import 'package:xoxknit/app/services/auth_service.dart';
import 'package:xoxknit/app/services/knitting/knitting_settings_service.dart';
import 'package:xoxknit/app/services/wizard_state_service.dart';
import '../controllers/new_item_wizard_controller.dart';
import 'dart:async';

class NewItemFormController extends GetxController {
  final formKey = GlobalKey<FormState>();
  final wizardController = Get.find<NewItemWizardController>();
  final wizardStateService = WizardStateService.to;
  final Rx<AutovalidateMode> autovalidateMode = AutovalidateMode.disabled.obs;

  // Tension values generation
  final List<String> tensionValues = List.generate(11, (i) => i) // 0 to 10
      .expand((whole) => List.generate(3, (j) => j) // 0, 1, 2 for decimal
          .map((frac) => '${whole}.${frac}'))
      .toList();
  // Default tension, e.g., "3.0"
  final RxString selectedTension = '3.0'.obs;

  // Text controllers
  late TextEditingController nameController;
  late TextEditingController stitchTypeController;
  late TextEditingController swatchNumberController;
  late TextEditingController startDateController;
  late TextEditingController neededByDateController;
  late TextEditingController yarnSupplierController;
  late TextEditingController yarnTitleController;
  late TextEditingController yarnCompositionController;
  late TextEditingController strandsController;
  late TextEditingController colorController;
  late TextEditingController weightController;
  late TextEditingController notesController;

  // Observable values
  final selectedMachine = Rxn<UserKnittingMachineModel>();
  final startDate = Rxn<DateTime>();
  final neededByDate = Rxn<DateTime>();
  final formSubmitted = false.obs;

  final knittingService = Get.find<KnittingSettingsService>();

  @override
  void onInit() {
    super.onInit();

    nameController = TextEditingController();
    stitchTypeController = TextEditingController();
    swatchNumberController = TextEditingController();
    startDateController = TextEditingController();
    neededByDateController = TextEditingController();
    yarnSupplierController = TextEditingController();
    yarnTitleController = TextEditingController();
    yarnCompositionController = TextEditingController();
    strandsController = TextEditingController();
    colorController = TextEditingController();
    weightController = TextEditingController();
    notesController = TextEditingController();

    // Set default timeline dates to today
    final today = DateTime.now();
    final formattedDate =
        DateFormat('MMM d, yyyy', Get.locale?.languageCode).format(today);

    startDate.value = today;
    neededByDate.value = today;
    startDateController.text = formattedDate;
    neededByDateController.text = formattedDate;

    // Listen to changes in the wizard state
    ever(wizardController.newItem, (NewItemModel item) {
      if (item.name != null) {
        initializeFormFromNewItem(item);
      }
    });

    // Initialize form with existing data if already available
    if (wizardController.newItem.value.name != null) {
      initializeFormFromNewItem(wizardController.newItem.value);
    }

    // Add listener for selected tension changes to update the model immediately
    // This ensures the form state reflects the UI state for the custom widget
    ever(selectedTension, (String tension) {
      // Update the underlying model if needed, or just ensure validation uses the Rx value
      // For now, we'll just let the submitForm use the selectedTension.value
    });
  }

  void initializeFormFromNewItem(NewItemModel item) {
    nameController.text = item.name ?? '';
    stitchTypeController.text = item.stitchType ?? '';
    swatchNumberController.text = item.swatchNumber ?? '';
    startDateController.text = item.startDate != null
        ? DateFormat('MMM d, yyyy', Get.locale?.languageCode)
            .format(item.startDate!)
        : '';
    neededByDateController.text = item.neededByDate != null
        ? DateFormat('MMM d, yyyy', Get.locale?.languageCode)
            .format(item.neededByDate!)
        : '';
    yarnSupplierController.text = item.yarnSupplier ?? '';
    yarnTitleController.text = item.yarnTitle ?? '';
    yarnCompositionController.text = item.yarnComposition ?? '';
    strandsController.text = item.strands?.toString() ?? '';
    colorController.text = item.color ?? '';
    weightController.text = item.weightOnHand?.toString() ?? '';
    // Ensure the loaded tension exists in our generated list, otherwise default
    selectedTension.value =
        tensionValues.contains(item.tension) ? item.tension! : '3.0';
    notesController.text = item.notes ?? '';

    // Find the matching machine from the available machines list
    if (item.knittingMachine != null) {
      final matchingMachine = knittingService.userMachines
          .firstWhereOrNull((m) => m.id == item.knittingMachine!.id);
      selectedMachine.value = matchingMachine;
    }

    startDate.value = item.startDate;
    neededByDate.value = item.neededByDate;
  }

  Future<void> initializeForm() async {
    final state = await wizardStateService
        .loadWizardState(wizardController.currentStateId.value!);
    if (state != null) {
      nameController.text = state.itemData.name ?? '';
      stitchTypeController.text = state.itemData.stitchType ?? '';
      swatchNumberController.text = state.itemData.swatchNumber ?? '';
      startDateController.text = state.itemData.startDate != null
          ? DateFormat('MMM d, yyyy', Get.locale?.languageCode)
              .format(state.itemData.startDate!)
          : '';
      neededByDateController.text = state.itemData.neededByDate != null
          ? DateFormat('MMM d, yyyy', Get.locale?.languageCode)
              .format(state.itemData.neededByDate!)
          : '';
      yarnSupplierController.text = state.itemData.yarnSupplier ?? '';
      yarnTitleController.text = state.itemData.yarnTitle ?? '';
      yarnCompositionController.text = state.itemData.yarnComposition ?? '';
      strandsController.text = state.itemData.strands?.toString() ?? '';
      colorController.text = state.itemData.color ?? '';
      weightController.text = state.itemData.weightOnHand?.toString() ?? '';
      // Ensure loaded tension is valid
      selectedTension.value = tensionValues.contains(state.itemData.tension)
          ? state.itemData.tension!
          : '3.0';
      notesController.text = state.itemData.notes ?? '';
      selectedMachine.value = state.itemData.knittingMachine;
      startDate.value = state.itemData.startDate;
      neededByDate.value = state.itemData.neededByDate;
      formSubmitted.value = state.isCompleted;
    }
  }

  @override
  void onClose() {
    nameController.dispose();
    stitchTypeController.dispose();
    swatchNumberController.dispose();
    startDateController.dispose();
    neededByDateController.dispose();
    yarnSupplierController.dispose();
    yarnTitleController.dispose();
    yarnCompositionController.dispose();
    strandsController.dispose();
    colorController.dispose();
    weightController.dispose();
    notesController.dispose();
    super.onClose();
  }

  Future<void> selectDate(BuildContext context,
      {required bool isStartDate}) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      locale: Get.locale,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
    );

    if (picked != null) {
      final formattedDate =
          DateFormat('MMM d, yyyy', Get.locale!.languageCode).format(picked);
      if (isStartDate) {
        startDate.value = picked;
        startDateController.text = formattedDate;
      } else {
        neededByDate.value = picked;
        neededByDateController.text = formattedDate;
      }
    }
  }

  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      // Use translation keys if available, otherwise fallback
      return 'newItemWizard_form_validation_enter'
          .trParams({'field': fieldName});
      // return 'Please enter $fieldName'; // Fallback
    }
    return null;
  }

  String? validateNumber(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      // Allow optional number fields
      return null;
    }
    if (double.tryParse(value) == null) {
      // Use translation keys if available, otherwise fallback
      return 'newItemWizard_form_validation_validNumber'
          .trParams({'field': fieldName});
      // return 'Please enter a valid number for $fieldName'; // Fallback
    }
    return null;
  }

  bool validateForm() {
    formSubmitted.value = true;

    // Simplified validation: Check main required fields visually indicated
    // The form's built-in validator handles individual fields upon interaction/submission attempt

    final isNameValid = nameController.text.isNotEmpty;
    final isMachineValid = selectedMachine.value != null;
    final isTensionValid =
        selectedTension.value.isNotEmpty; // Should always be true
    final isYarnTitleValid = yarnTitleController.text.isNotEmpty;
    final isStitchTypeValid = stitchTypeController.text.isNotEmpty;

    if (!isNameValid) {
      // Get.snackbar(
      //   'newItemWizard_form_validation_required'
      //       .trParams({'field': 'newItemWizard_form_itemName'.tr}),
      //   'newItemWizard_form_itemName'.tr,
      //   snackPosition: SnackPosition.BOTTOM,
      //   backgroundColor: Colors.red.shade100,
      // );
      return false;
    }

    if (!isMachineValid) {
      // Get.snackbar(
      //   'newItemWizard_form_validation_required'
      //       .trParams({'field': 'newItemWizard_form_machine'.tr}),
      //   'newItemWizard_form_machine'.tr,
      //   snackPosition: SnackPosition.BOTTOM,
      //   backgroundColor: Colors.red.shade100,
      // );
      return false;
    }

    // No specific check for tension needed here as it has a default and is controlled
    // if (!isTensionValid) { ... }

    if (!isYarnTitleValid) {
      // Get.snackbar(
      //   'newItemWizard_form_validation_required'
      //       .trParams({'field': 'newItemWizard_form_yarnTitle'.tr}),
      //   'newItemWizard_form_yarnTitle'.tr,
      //   snackPosition: SnackPosition.BOTTOM,
      //   backgroundColor: Colors.red.shade100,
      // );
      return false;
    }

    // Add validation check for Stitch Type
    if (!isStitchTypeValid) {
      // Consider adding a snackbar similar to others if desired
      return false;
    }

    // Also trigger the Form's validation
    if (!(formKey.currentState?.validate() ?? false)) {
      autovalidateMode.value = AutovalidateMode.always;
      return false;
    }

    return true;
  }

  void submitForm() {
    if (validateForm()) {
      final newItem = NewItemModel(
        name: nameController.text,
        stitchType: stitchTypeController.text,
        swatchNumber: swatchNumberController.text.isNotEmpty
            ? swatchNumberController.text
            : null,
        startDate: startDate.value,
        neededByDate: neededByDate.value,
        yarnSupplier: yarnSupplierController.text.isNotEmpty
            ? yarnSupplierController.text
            : null,
        yarnTitle: yarnTitleController.text, // Required
        yarnComposition: yarnCompositionController.text.isNotEmpty
            ? yarnCompositionController.text
            : null,
        strands: int.tryParse(strandsController.text),
        color: colorController.text.isNotEmpty ? colorController.text : null,
        weightOnHand: double.tryParse(weightController.text),
        knittingMachine: selectedMachine.value!, // Required
        tension: selectedTension.value, // Required, always has a value
        notes: notesController.text.isNotEmpty ? notesController.text : null,
      );

      wizardController.updateItemDetails(newItem);
    } else {
      autovalidateMode.value = AutovalidateMode.always;
      // Get.snackbar(
      //   'newItemWizard_form_validation_errors'.tr,
      //   'newItemWizard_form_validation_checkFields'.tr,
      //   snackPosition: SnackPosition.BOTTOM,
      //   backgroundColor: Colors.orange.shade100,
      // );
    }
  }

  // Show dialog to add a new machine
  void showAddMachineDialog() {
    final searchController = TextEditingController();
    final searchQuery = ''.obs;

    searchController.addListener(() {
      searchQuery.value = searchController.text.toLowerCase();
    });

    // Use a completer to handle the selection result
    final completer = Completer<KnittingMachineModel?>();

    showDialog<KnittingMachineModel?>(
      context: Get.context!,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: Get.width * 0.9,
          constraints: BoxConstraints(
            maxWidth: 600,
            maxHeight: Get.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(24.0),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.primary,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'userMachines_addMachine'.tr,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'userMachines_machineDialog_selectYourMachine'.tr,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withOpacity(0.8),
                          ),
                    ),
                  ],
                ),
              ),

              // Search field
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: 'userMachines_machineDialog_searchMachines'.tr,
                    prefixIcon: Icon(Icons.search, color: Colors.grey.shade700),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    filled: true,
                    fillColor: Colors.grey.shade50,
                  ),
                ),
              ),

              // Machine list
              Expanded(
                child: Obx(() {
                  final query = searchQuery.value;
                  final filteredMachines = knittingService.knittingMachines
                      .where((machine) =>
                          query.isEmpty ||
                          machine.fullName.toLowerCase().contains(query) ||
                          machine.type.toLowerCase().contains(query) ||
                          machine.machineClass.toLowerCase().contains(query) ||
                          machine.mainBrand.toLowerCase().contains(query))
                      .toList();

                  return filteredMachines.isEmpty
                      ? Center(
                          child: Text(
                            'userMachines_machineDialog_noMachinesFound'.tr,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: filteredMachines.length,
                          itemBuilder: (context, index) {
                            final machine = filteredMachines[index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                                side: BorderSide(
                                  color: Colors.grey.shade300,
                                  width: 1,
                                ),
                              ),
                              child: InkWell(
                                onTap: () {
                                  // Return the selected machine as dialog result
                                  Navigator.of(context).pop(machine);
                                },
                                borderRadius: BorderRadius.circular(12),
                                child: Padding(
                                  padding: const EdgeInsets.all(12),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 40,
                                        height: 40,
                                        decoration: BoxDecoration(
                                          color: Get.theme.colorScheme
                                              .primaryContainer,
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.precision_manufacturing,
                                          color: Get.theme.colorScheme.primary,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              machine.fullName,
                                              style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                                color: Get.theme.colorScheme
                                                    .onSurface,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'userMachines_machineInfo_needles'
                                                  .trParams({
                                                "count": machine.needlesCount
                                                    .toString()
                                              }),
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey.shade700,
                                              ),
                                            ),
                                            Text(
                                              '${machine.type} - ${machine.machineClass}',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey.shade600,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                }),
              ),

              // Cancel button
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(null),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      child: Text(
                        'common_cancel'.tr,
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ).then((selectedMachine) {
      // Clean up controller
      searchController.dispose();

      // Show naming dialog if a machine was selected
      if (selectedMachine != null) {
        _showMachineNamingDialog(selectedMachine);
      }
    });
  }

  // Show dialog to name a newly selected machine
  void _showMachineNamingDialog(KnittingMachineModel baseMachine) {
    final nameController = TextEditingController(
        text: 'newItemWizard_form_machineNaming_defaultName'
            .trParams({'machineName': baseMachine.fullName}));
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: Get.context!,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: Get.width * 0.9,
          constraints: const BoxConstraints(
            maxWidth: 500,
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'newItemWizard_form_machineNaming_title'.tr,
                style: Get.textTheme.titleMedium,
              ),
              const SizedBox(height: 16),

              // Selected machine info display
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Get.theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'newItemWizard_form_machineNaming_selectedMachine'.tr,
                      style: TextStyle(
                        fontSize: 12,
                        color: Get.theme.colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.precision_manufacturing,
                          color: Get.theme.colorScheme.primary,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                baseMachine.fullName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                'userMachines_machineInfo_needles'.trParams({
                                  "count": baseMachine.needlesCount.toString()
                                }),
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey.shade700,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name field
                    Text(
                      'newItemWizard_form_machineNaming_nameLabel'.tr,
                      style: Get.textTheme.bodySmall,
                    ),
                    const SizedBox(height: 8),
                    TextFormField(
                      controller: nameController,
                      decoration: InputDecoration(
                        hintText:
                            'newItemWizard_form_machineNaming_nameHint'.tr,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'newItemWizard_form_machineNaming_nameHint'.tr;
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Action buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text('common_cancel'.tr),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: () {
                      if (formKey.currentState!.validate()) {
                        final newMachine = UserKnittingMachineModel(
                          userId:
                              AuthService.to.currentUser.value!.shopifyIdNumber,
                          id: baseMachine.id,
                          customName: nameController.text.isNotEmpty
                              ? nameController.text
                              : baseMachine.fullName,
                          notes: "",
                          type: baseMachine.type,
                          baseModelId: baseMachine.id,
                          machineClass: baseMachine.machineClass,
                          mainBrand: baseMachine.mainBrand,
                          model: baseMachine.model,
                          needlePitch: baseMachine.needlePitch,
                          needlesCount: baseMachine.needlesCount,
                        );

                        KnittingSettingsService.to.addUserMachine(newMachine);
                        selectedMachine.value = newMachine;
                        Get.back();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Get.theme.colorScheme.primary,
                      foregroundColor: Get.theme.colorScheme.onPrimary,
                    ),
                    child: Text('common_save'.tr),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
