{"app": {"title": "xoxknit"}, "auth": {"errors": {"customerDisabled": "This account has been disabled", "emailInUse": "An account already exists with this email", "invalidCredentials": "Invalid email or password", "operationNotAllowed": "This type of account is currently disabled. Please contact support.", "resetFailed": "Failed to send password reset email. Please try again.", "signOutFailed": "Failed to sign out from all services", "unconfirmedEmail": "Please verify your email address", "userNotFound": "No account found with this email", "weakPassword": "Password is too weak", "wrongPassword": "Incorrect password", "noUser": "No authenticated user found to delete", "noAccessToken": "No valid Shopify session found", "firebaseDeletionFailed": "Failed to delete your authentication account", "accountDeletionFailed": "Failed to delete account: {message}", "unknownDeletionError": "Unknown error occurred", "shopifyGeneric": "An error occurred with Shopify authentication", "firebaseGeneric": "An error occurred with Firebase authentication"}}, "common": {"add": "Add", "apply": "Apply", "back": "Back", "cancel": "Cancel", "close": "Close", "confirm": "Confirm", "continue": "Continue", "daysAgo": "@days days ago", "delete": "Delete", "done": "Done", "edit": "Edit", "email": "Email", "error": "Error", "finished": "Finished!", "forgotPassword": "Forgot password?", "help": "Help & documentation", "loading": "Loading...", "next": "Next", "no": "No", "ok": "OK", "password": "Password", "previous": "Previous", "print": "Print Pattern", "refresh": "Refresh", "remove": "Remove", "retry": "Retry", "save": "Save", "saveAndExit": "Save and Exit", "startKnitting": "Start Knitting", "success": "Success", "today": "Today", "undo": "Undo", "untitled": "Untitled", "warning": "Warning", "yes": "Yes", "yesterday": "Yesterday", "stopKnitting": "Stop knitting?"}, "errors": {"dataLoadFailed": "Failed to load data", "generationFailed": "Failed to generate knitting instructions: @error", "networkError": "Network error. Please check your connection.", "noCurrentInstructionFound": "No current instruction found", "noInstructionsAvailable": "No instructions available", "operationFailed": "Operation failed. Please try again.", "partialSignOut": "Partial sign out error occurred", "saveFailed": "Failed to save changes", "shapeTooSmall": "Shape is too small to generate a pattern", "signOutFailed": "Sign out failed, please try again", "unexpected": "An unexpected error occurred. Please try again.", "unknownError": "An unexpected error occurred", "shapeOutOfBounds": "<PERSON><PERSON><PERSON> would go out of bounds.", "rotationPrevented": "Rotation prevented, shape would go out of bounds."}, "help": {"categories": {"advancedTechniques": "Advanced techniques", "gettingStarted": "Getting started", "gridFeatures": "Grid features", "keyboardShortcuts": "Keyboard shortcuts", "shapeTools": "Shape tools"}, "collapse": "Collapse", "expand": "Expand", "search": {"clear": "Clear search", "hint": "Search help topics..."}, "title": "Help & documentation", "topics": {"advancedTechniques": {"curveMode": {"description": "Activate curve mode to create smooth, curved shapes. Edit the curves by dragging the control points.", "title": "Using curve mode"}, "groupingShapes": {"description": "Group multiple shapes to manipulate them as a single unit. You can select the group to move, resize, or rotate all shapes together.", "title": "Grouping shapes"}, "mirrorMode": {"description": "Toggle mirror mode to automatically create mirror copies of your shapes. Great for symmetrical designs.", "title": "Mirror mode"}, "snapToCenter": {"description": "Use the snap to center feature to precisely align shapes to the center of your work area. This helps create balanced, symmetrical designs.", "title": "Snap to center"}, "undo_redo": {"description": "Undo/redo functionality lets you step back through your changes or restore them if needed. Use the icons in the toolbar or keyboard shortcuts.", "title": "Undo and redo"}}, "gettingStarted": {"addingShapes": {"description": "Add shapes using the toolbar on the left side. Choose from rectangle, triangle, right triangle, and trapezoid. Click to add the shape to your canvas.", "title": "Adding shapes"}, "multiSelection": {"description": "Enable multi-selection mode using the toggle in the top-left corner. This allows you to select and modify multiple shapes at once.", "title": "Multi-selection"}, "selectingShapes": {"description": "Click on a shape to select it, then drag to move it. Use the handles to resize or rotate the shape.", "title": "Selecting and moving shapes"}, "usingToolbar": {"description": "The toolbar on the left provides tools organized into categories: shape tools, transform tools, grouping tools, and utility tools.", "title": "Using the toolbar"}}, "gridFeatures": {"centerSnapping": {"description": "Enable center snapping to align shapes to the center line. This helps create symmetrical designs and ensures shapes align perfectly.", "title": "Center snapping"}, "gridSnapping": {"description": "Toggle grid snapping on/off with the grid icon in the toolbar. When enabled, shapes will automatically align to the grid.", "title": "Grid snap"}, "needlePositioning": {"description": "The property panel shows needle positions using L/R notation. You can directly edit positions to place shapes precisely on specific needles.", "title": "Needle positioning"}, "unitToggle": {"description": "Toggle between showing dimensions in stitches/rows or centimeters. This helps you visualize the real-world size of your knitting pattern.", "title": "Unit toggling"}, "zoomAndPan": {"description": "Pinch to zoom in/out, drag with two fingers to pan. The grid adjusts automatically with zoom level.", "title": "Zoom and pan"}}, "keyboardShortcuts": {"common": {"description": "ESC: Deselect all shapes\nDEL: Delete selected shape(s)\nCtrl+Z: Undo\nCtrl+Y/Ctrl+Shift+Z: Redo", "title": "Common shortcuts"}, "manipulation": {"description": "H: Flip horizontally\nV: Flip vertically\nR: Rotate 45° clockwise\nD: Duplicate selected shape", "title": "Shape manipulation"}, "selection": {"description": "Shift+Click: Select multiple shapes\nCtrl+A: Select all shapes\nCtrl+G: Group selected shapes\nCtrl+Shift+G: Ungroup", "title": "Selection shortcuts"}}, "shapeTools": {"availableShapes": {"description": "The editor offers four basic shapes: rectangle, triangle, right triangle, and trapezoid. Each can be customized with the property panel.", "title": "Available shapes"}, "propertyHud": {"description": "The property panel shows dimensions, position, and rotation. You can make precise adjustments by entering values directly.", "title": "Property panel"}, "resizing": {"description": "Resize shapes by dragging the corner handles or by entering exact dimensions in the property panel. Dimensions can be displayed in stitches/rows or centimeters.", "title": "Resizing shapes"}, "rotation": {"description": "Rotate shapes by dragging the rotation handle or by entering an exact angle in the property panel. You can also use the rotate button for 45° increments.", "title": "Rotating shapes"}, "transformations": {"description": "Transform tools include flip horizontal, flip vertical, duplicate, and rotate. These tools help you quickly modify shapes.", "title": "Shape transformations"}}}}, "home": {"drawer": {"calculators": "Calculators", "courses": "Courses", "helpSupport": "Help & support", "home": "Home", "logout": "Logout", "settings": "Settings", "shop": "Shop"}, "projectsGrid": {"comingSoon": "Coming soon!", "myItems": "My items", "newItem": "New item", "xoxknitItems": "xoxknit items"}, "title": "Home"}, "knittingInstructions": {"interactive_patternVisualization": "Pattern Visualization", "interactive_visualizer_noPatternCreateFirst": "No pattern available. Please create a pattern first.", "print_printSent": "Print", "print_printingFutureUpdate": "Printing functionality would be implemented here", "bottomWidth": "Bottom width", "description": "Description", "errors": {"calculationFailed": "Failed to calculate pattern statistics", "generationFailed": "Failed to generate knitting instructions"}, "generalInfo": "General info", "height": "Height", "interactive": {"basicInstructions": "Basic instructions:", "completeAndContinue": "Done and continue to rows @rows", "completeSequence": "Complete sequence", "completionMessage": "Congratulations! You have completed knitting this pattern.", "confirmClose": "Are you sure you want to cancel knitting instructions and return to the shape designer?", "endingRowCounter": "Ending row counter", "helpTitle": "How to use the knitting view", "loadingInstructions": "Loading knitting instructions...", "zoneCompleted": "Zone @zoneName Completed", "zoneCompletedMessage": "Great work! You have completed @completedZone. Ready to start the next zone (@nextZone)?", "restartZone": "Restart zone", "startNextZone": "Start next zone", "nowKnitting": "Now knitting: @zoneName", "patternCompleted": "<PERSON><PERSON> Completed!", "patternCompletedMessage": "Congratulations! You have completed knitting the entire pattern.", "whatNext": "What would you like to do next?", "startOver": "Start Over", "myItems": "My Items", "patternResetMessage": "Pattern reset to beginning", "noZonesAvailable": "No knitting zones available", "noInstructionsAvailable": "No knitting instructions available", "configureZonesFirst": "Please configure knitting zones first", "shapeDataMissing": "Shape data may be missing or instructions were not generated", "regenerateInstructions": "Regenerate Instructions", "completeZoneAndContinue": "Complete Zone and Continue", "movingToPrevious": "Moving to previous instruction", "customSettings": "Custom Settings: @settings", "repeatForRows": "Repeat for @count rows: @ranges", "discontinuousPattern": "Discontinuous pattern", "instructions": {"completeButton": "• Press \"Complete & continue\" or \"Complete sequence\" after finishing the current step", "followHighlighted": "• Follow the highlighted knitting instructions for each step", "glassesIcon": "• Tap the glasses icon in the app bar to preview the entire pattern", "groupedRows": "• Groups of identical rows are treated as a single instruction step", "needleRanges": "• Needle ranges are highlighted in purple for better visibility", "previousButton": "• Use the \"Previous\" button to go back to the previous instruction", "printIcon": "• Use the print icon to print your pattern for offline reference", "progressBar": "• The progress bar advances after completing an entire sequence, not individual rows", "rowCounter": "• A counter shows which repeated row you're currently working on", "rowsXY": "• You'll see \"Rows X-Y\" in the title and a label showing how many times to repeat", "showPatternView": "• Tap \"Show pattern view\" at the bottom to see the full pattern", "zoomPan": "• In the pattern view, you can zoom and pan to see details"}, "knittingInstructions": "Knitting instructions", "machineInfo": {"gauge": "@stitches st/cm × @rows rows/cm", "notSelected": "Not selected"}, "navigationTools": "Navigation & tools:", "needleSettings": "Needle settings", "patternHelp": {"additionalWarnings": "• Additional warnings may appear for specific row types", "changeRows": "• Change: rows with increases or decreases", "discontinuousRows": "• Discontinuous: rows with gaps in the stitch pattern", "interactiveTitle": "Interactive features", "lightGrayCells": "• Light gray cells represent needles to skip (no stitch)", "purpleCells": "• Purple cells represent stitches to knit", "repeatRows": "• Repeat: multiple identical rows that can be knit together", "rowTypesTitle": "Row types & indicators", "symmetricRows": "• Symmetric: rows with mirror symmetry in the pattern", "tapRow": "• Tap on a row to see detailed instructions", "tapStitches": "• Tap on individual stitches for details", "title": "Pattern visualization help", "toggleView": "• Toggle between grid and text view", "understandingTitle": "Understanding the visualization", "warningsTitle": "Warnings", "wideGaps": "• Wide gaps: Unusually large spaces between stitches", "zoomControls": "• Use zoom controls to resize the grid"}, "patternVisualization": "Pattern visualization:", "preview": {"blueStitches": "Blue stitches", "magentaStitches": "Magenta stitches", "needles": "Needles L@left to R@right", "noKnittingPatternAvailable": "No knitting pattern available to preview", "patternPreview": "Pattern preview: @name", "previewNotAvailable": "Preview not available", "rowsTall": "@rows rows tall", "showGrid": "Show grid", "showYarnConnections": "Show yarn connections", "stitchesWide": "@width stitches wide"}, "previousRows": "Back to rows @rows", "previousStep": "Previous step", "progressTracker": "Progress tracker", "repeatProgress": "Row @current of @total", "repeatingRows": "Repeating rows:", "rowInstructions": "Row @row", "rowsInstructions": "Rows @start-@end", "startingRowCounter": "Starting row counter", "stayHere": "Stay here", "visualizer": {"noPatternAvailable": "No pattern available", "noPatternCreateFirst": "No pattern available. Please create a pattern first.", "pattern": "Pattern:", "patternView": "Pattern view", "patternVisualization": "Full pattern visualization from L@needles to R@needles.", "zoomPanInstructions": "Use pinch gesture to zoom and pan to see details."}}, "machineDetails": "Machine details", "maximumNeedles": "Maximum needles", "measurements": "Measurements", "needlesAvailable": "Needles available", "needlesRequired": "Needles required", "print": {"failedToPrint": "Failed to print pattern: @error", "noPatternAvailable": "No pattern available to print", "patternSentToPrinter": "<PERSON><PERSON> sent to printer", "printError": "Print error", "printSent": "Print sent", "printingFutureUpdate": "Pattern printing will be implemented in a future update", "savingFutureUpdate": "Pattern saving will be implemented in a future update"}, "rows": "Rows", "shapeDesign": "Shape design", "stitches": "Stitches", "strands": "Strands", "summary": {"bottomWidth": "Bottom width", "cm": "cm", "description": "Description", "generalInfo": "General info", "height": "Height", "machine": {"needlesAvailable": "Needles available", "needlesRequired": "Needles required"}, "machineDetails": "Machine details", "maximumNeedles": "Maximum needles", "rows": "rows", "shapeDesign": "Shape design", "stitches": "stitches", "title": "Knitting instructions summary", "topWidth": "Top width", "totalRowsToKnit": "Total rows to knit", "totalYarnRequired": "Total yarn required", "yarn": {"strands": "@count strands", "supplier": "Supplier: @supplier", "tension": "Tension: @tension", "title": "Yarn title: @title"}, "yarnSpecifications": "Yarn specifications"}, "topWidth": "Top width", "totalRowsToKnit": "Total rows to knit", "totalYarnRequired": "Total yarn required", "yarnSpecifications": "Yarn specifications"}, "login": {"emailHint": "Enter your email", "loginButton": "<PERSON><PERSON>", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "noAccount": "Don't have an account?", "passwordHint": "Enter your password", "signUpLink": "Sign up", "title": "xoxknit", "validation": {"emailInvalid": "Please enter a valid email", "emailRequired": "Email is required", "passwordLength": "Password must be at least 6 characters", "passwordRequired": "Password is required"}, "welcomeBack": "Welcome back!"}, "myItems": {"archivedItems": "Archived Items", "inProgress": "In Progress", "completed": "Completed", "deleteConfirmation": {"message": "Are you sure you want to delete this item? This action cannot be undone.", "title": "Delete item"}, "emptyStateActive": {"createButton": "Create new item", "message": "Start by creating your first knitting item", "title": "No items yet !"}, "emptyStateArchived": {"message": "Archived items will appear here", "title": "No archived items !", "viewActiveButton": "View active items"}, "itemCard": {"archive": "Archive", "completed": "Completed", "continue": "Continue", "markComplete": "Mark as complete", "markIncomplete": "Mark as incomplete", "knitAgain": "<PERSON><PERSON><PERSON> again", "unarchive": "Unarchive", "untitledItem": "Untitled item"}, "refreshItems": "Refresh Items", "showActive": "Show active items", "showArchived": "Show archived items", "snackbar": {"archiveError": "Failed to archive item", "archivedMessage": "The item has been archived", "deleteError": "Failed to delete item", "itemArchived": "Item archived", "itemRestored": "Item has been restored", "loadError": "Failed to load items", "markedCompleted": "Item marked as completed", "markedIncomplete": "Item marked as incomplete", "markCompletedError": "Failed to mark item as completed", "markIncompleteError": "Failed to mark item as incomplete", "success": "Success", "successDeleted": "Item deleted successfully", "successUnarchived": "Item unarchived", "unarchiveError": "Failed to unarchive item", "undoButton": "Undo", "undoComplete": "Undo complete"}, "stepDescription": {"completed": "Completed", "gaugeCalculator": "Gauge calculator", "interactiveKnitting": "Interactive knitting", "itemDetails": "Item details", "knittingZoneConfig": "Knitting Zone Configuration", "patternSummary": "Pattern summary", "shapeEditor": "Shape editor", "unknownStep": "Unknown step"}, "title": "My Items"}, "newItemWizard": {"back": "Back", "cancel": "Cancel", "confirmCancel": {"continue": "Continue editing", "discard": "Discard", "message": "Are you sure you want to discard your changes?", "title": "Discard changes?"}, "continue": "Continue", "done": "Done", "errors": {"addShapeRequired": "Please add at least one shape before continuing", "loadFailed": "Failed to load item data", "saveFailed": "Failed to save item", "shapeEditorLoading": "Shape editor is loading...", "shapeEditorNotFound": "Shape editor not found. Please try again.", "title": "Error"}, "form": {"addNewMachine": "Add new machine...", "additionalNotes": "Additional notes", "color": "Color", "composition": "Composition", "description": "Description", "itemName": "Item name", "machine": "Machine", "neededBy": "Needed by", "optionalInformation": "Optional information", "requiredInformation": "Required information", "startDate": "Start date", "strands": "Strands", "supplier": "Supplier", "swatchNumber": "Swatch #", "tension": "Tension", "validation": {"number": "Please enter a valid number", "required": "Please enter @field", "enter": "Please enter @field", "validNumber": "Please enter a valid number for @field", "checkFields": "Please check all required fields"}, "saved": "Saved", "detailsUpdated": "Item details updated successfully", "validation_errors": "Validation Error", "validation_checkFields": "Please check all required fields", "yarnOnHand": "Yarn on hand (g)", "yarnTitle": "Yarn title", "stitchType": "Stitch type", "machineNaming": {"title": "Name Your Machine", "selectedMachine": "Selected machine:", "nameLabel": "Machine name", "nameHint": "Enter a nickname for this machine", "defaultName": "My-@machineName"}}, "generatingInstructions": "Generating knitting instructions...", "loadingDialog": {"generating": "Generating knitting instructions..."}, "navigation": {"next": "Next", "previous": "Previous"}, "next": "Next", "save": "Save", "steps": {"createNewItem": "Create new item", "gaugeCalculator": "Gauge calculator", "interactiveKnitting": "Interactive knitting", "itemDetails": "Item details", "knittingZoneConfig": "Knitting Zone Configuration", "patternSummary": "Pattern summary", "shapeEditor": "Shape editor", "zonesEditor": "Zones editor"}, "success": {"saved": "<PERSON><PERSON> saved successfully"}, "title": "Create new item"}, "patternPrinter": {"finished": "Finished!", "gaugeCalculation": "Gauge calculation", "generalInfo": "General info", "itemDescription": "Item description", "jersey": "Jersey", "knitRows": "Knit @count rows", "knitting": {"noStitches": "No stitches to knit"}, "knittingInstructions": "knitting instructions", "leftNeedleSetting": "Left needle setting", "machineType": "Machine type", "measurements": "MEASUREMENTS", "page": "page @current of @total", "results": "RESULTS", "rightNeedleSetting": "Right needle setting", "rowsInSwatch": "5 - rows in swatch", "rowsPer10Cm": "Rows per 10cm:", "rowsToKnit": "Rows to knit", "shapeDesign": "Shape design", "startRow": "Start row", "stitchType": "Stitch type", "stitchesInSwatch": "4 - stitches in swatch", "stitchesPer10Cm": "Stitches per 10cm:", "supplier": "Supplier", "swatchLength": "2 - swatch length", "swatchWeight": "3 - swatch weight", "swatchWidth": "1 - swatch width", "tensionSetting": "Tension setting", "totalYarnRequired": "Total yarn required", "yarnRequirements": "Yarn requirements", "yarnTitleAndStrands": "Yarn title + strands"}, "projectsView": {"createProject": "Create new project", "filter": "Filter projects", "noProjects": "No projects yet", "sort": "Sort by", "title": "Projects"}, "resetPassword": {"emailHint": "Enter your email address", "emailLabel": "Email", "heading": "Forgot your password?", "instructions": "Enter your email address and we'll send you instructions to reset your password.", "resetButton": "Reset password", "successMessage": "Password reset instructions have been sent to your email.", "title": "Reset password", "validation": {"emailInvalid": "Please enter a valid email", "emailRequired": "Email is required"}}, "settings": {"appearance": {"darkMode": {"subtitle": "Toggle dark mode theme", "title": "Dark Mode"}}, "currency": {"dialogTitle": "Select currency", "eur": "EUR (€)", "subtitle": "Select your preferred currency", "title": "<PERSON><PERSON><PERSON><PERSON>", "usd": "USD ($)"}, "knittingMachines": {"subtitle": "Manage your knitting machines", "title": "My machines"}, "language": {"dialogTitle": "Select language", "subtitle": "Select your preferred language", "title": "App language"}, "measurement": {"centimeters": "Centimeters", "dialogTitle": "Select units", "inches": "Inches", "subtitle": "Choose your preferred unit system", "title": "Units"}, "sections": {"appearance": "Appearance", "currency": "<PERSON><PERSON><PERSON><PERSON>", "knittingMachines": "Knitting machines", "language": "Language", "measurement": "Measurement", "accountManagement": "Account Management"}, "accountManagement": {"deleteAccount": {"title": "Delete account", "subtitle": "Delete all your data including projects, machines and patterns", "confirmTitle": "Delete account?", "confirmMessage": "This will permanently delete your account and all associated data. This action cannot be undone.", "typeToConfirm": "Type DELETE to confirm", "confirm": "Delete my account", "successTitle": "Account deleted", "successMessage": "Your account has been successfully deleted", "errorMessage": "Failed to delete account. Please try again.", "deletingMessage": "Deleting your account..."}}, "title": "Settings"}, "shapeEditor": {"confirmReset": {"cancel": "Cancel", "confirm": "Reset", "message": "Reset the shape? All changes will be lost.", "title": "Reset shape?"}, "controls": {"generate": "Generate pattern", "mirror": "Mirror", "previewPattern": "Preview pattern", "redo": "Redo", "reset": "Reset", "save": "Save", "undo": "Undo"}, "errors": {"saveFailed": "Failed to save shape", "shapeInvalid": "Invalid shape configuration", "shapeTooSmall": "Shape is too small to generate a pattern"}, "groupInfo": {"dimensions": {"center": "Group center: (@x, @y)", "height": "Group height: @height px", "rotation": "Rotation:", "width": "Group width: @width px"}, "numberOfShapes": "Number of shapes: @count", "shapeTypes": "Shape types:", "title": "Group information"}, "help": {"categories": {"advancedTechniques": "Advanced techniques", "gettingStarted": "Getting started", "gridFeatures": "Grid features", "keyboardShortcuts": "Keyboard shortcuts", "shapeTools": "Shape tools"}, "topics": {"creatingFirstShape": {"description": "Click the + button in the toolbar and select a shape. Then click on the canvas to place it. You can resize and rotate shapes using the handles.", "title": "Creating your first shape"}, "editingShortcuts": {"description": "Ctrl+G: Group selected shapes\nCtrl+Shift+G: Ungroup\nH: Flip horizontally\nV: Flip vertically\nDelete: Remove selected shape", "title": "Editing shortcuts"}, "flippingShapes": {"description": "Use the flip buttons in the toolbar or press H/V keys to flip selected shapes horizontally or vertically.", "title": "Flipping shapes"}, "gridLabels": {"description": "Show or hide grid labels using the label icon. Labels show needle positions using L/R notation, with L1 and R1 at the center.", "title": "Grid labels"}, "gridSnapping": {"description": "Toggle grid snapping on/off with the grid icon in the toolbar. When enabled, shapes will automatically align to the grid.", "title": "Grid snapping"}, "groupingShapes": {"description": "Select multiple shapes with <PERSON>ft+Click, then press Ctrl+G or use the group button to group them together.", "title": "Grouping shapes"}, "historyShortcuts": {"description": "Ctrl+Z: Undo\nCtrl+Y or Ctrl+Shift+Z: Redo", "title": "History shortcuts"}, "mirrorMode": {"description": "Toggle mirror mode to automatically create mirror copies of your shapes. Great for symmetrical designs.", "title": "Mirror mode"}, "realWorldDimensions": {"description": "Shape dimensions can be displayed in centimeters based on your knitting machine's needle pitch. Toggle between grid units and cm using the unit button in the shape properties panel. At maximum zoom (500%), each grid cell represents one needle.", "title": "Item dimensions in centimeters"}, "scrollableToolbar": {"description": "The toolbar at the top of the screen is scrollable. Swipe left or right to access more tools. Look for the arrow indicators at the edges.", "title": "Using the scrollable toolbar"}, "selectingMovingShapes": {"description": "Click on a shape to select it, then drag to move it. Use the handles to resize or rotate the shape.", "title": "Selecting and moving shapes"}, "selectionShortcuts": {"description": "Shift+Click: Select multiple shapes\nCtrl+A: Select all shapes\nEsc: Deselect all", "title": "Selection shortcuts"}, "shapeProperties": {"description": "View and edit shape properties in the HUD panel. You can adjust position, size, and rotation precisely.", "title": "Shape properties"}, "usingCurveMode": {"description": "Activate curve mode to create smooth, curved shapes. Edit the curves by dragging the control points.", "title": "Using curve mode"}, "workingWithGroups": {"description": "Group shapes to manipulate them together. You can also edit individual shapes within a group using the group properties panel.", "title": "Working with groups"}, "zoomPan": {"description": "Pinch to zoom in/out, drag with two fingers to pan. The grid adjusts automatically with zoom level.", "title": "Zoom and pan"}}}, "propertyHud": {"group": "Group", "shape": "<PERSON><PERSON><PERSON>", "size": "Size", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "widthShort": "W", "heightShort": "H", "hidePanel": "Hide properties panel", "units": {"cm": "cm", "st": "st", "rw": "rw", "stRw": "st/rw"}, "errors": {"maxSize": "Max size: @width@unit × @height@unit", "maxWidth": "Max width: @width@unit", "maxHeight": "Max height: @height@unit", "invalidRotation": "Invalid rotation value. Please enter a whole number.", "rotationPrevented": "Rotation prevented. <PERSON><PERSON><PERSON> would go out of bounds.", "invalidNeedleFormat": "Invalid needle format. Use 0, L#, or R# format.", "moveConstrained": "Move constrained by boundaries or mirror mode.", "invalidNumber": "Invalid number format.", "positive": "Dimensions must be positive.", "generic": "Error applying changes.", "rotationGeneric": "Error applying rotation.", "positionGeneric": "Error applying position.", "gaugeSetup": "Invalid gauge setup.", "shapeStateNull": "Error: Shape state became null after update."}, "history": {"resizeGroup": "Resize Group", "manualSizeEdit": "Manual Size Edit", "position": "Adjust Position", "moveLeft": "Move Left", "moveRight": "Move Right"}, "position": "Position", "moveLeft": "Move Left @count Needles", "moveRight": "Move Right @count Needles", "rotation": "Rotation", "rotateLeft": "Rotate Counter-clockwise", "rotateRight": "Rotate Clockwise", "snapToCenter": "Snap to Center", "tooltip_showingMetric": "Showing real-world dimensions in centimeters based on gauge", "tooltip_showingGrid": "Showing grid dimensions in stitches (st) and rows (rw). Switch to cm.", "mirrorMode_active": "Mirror Mode Active", "snapDisabled": "Snap to Center is disabled while mirror mode is active."}, "shapes": {"group": "Group", "rectangle": "Rectangle", "rightTriangle": "Right triangle", "trapezoid": "Trapezoid", "triangle": "Triangle"}, "title": "Shape editor", "toolbar": {"actions": {"addShape": "Add shape", "centerLineSnapping": {"disable": "  snap to center line", "enable": "✓ snap to center line"}, "deleteShape": "Delete shape", "duplicateShape": "Duplicate shape", "flipHorizontal": "Flip horizontally", "flipVertical": "Flip vertically", "gridSnapping": {"disable": "  snap to grid", "enable": "✓ snap to grid"}, "group": "Group shapes", "mirrorMode": {"disable": "  mirror mode", "enable": "✓ mirror mode"}, "redo": "Redo", "resetZoom": "Reset Zoom", "rotate": "Rotate", "undo": "Undo", "ungroup": "Ungroup shapes", "vertexEditMode": {"disable": "Disable Vertex Edit Mode", "enable": "Enable Vertex Edit Mode"}}, "addShapes": "Add shapes", "actions_cut": "Cut", "actions_copy": "Copy", "actions_paste": "Paste", "actions_duplicate": "Duplicate", "actions_delete": "Delete", "actions_group": "Group", "actions_ungroup": "Ungroup", "actions_selectObjects": "Select Objects", "actions_flipHorizontal": "<PERSON><PERSON>", "actions_flipVertical": "<PERSON><PERSON>", "actions_more": "More", "actions_back": "Back", "customShape": "Custom Shape", "curveMode": "Curve mode", "deleteShape": "Delete shape", "disableCenterLineSnapping": "  Snap-to-center-line", "disableGridSnapping": "  Snap-to-grid", "disableMirrorMode": "Disable mirror mode", "duplicateShape": "Duplicate shape (D key)", "editCurves": "Edit curves", "enableCenterLineSnapping": "✓ Snap-to-center-line", "enableGridSnapping": "✓ Snap-to-grid", "exitCurveMode": "Exit curve mode", "flipHorizontally": "Flip horizontally (H key)", "flipVertically": "Flip vertically (V key)", "group": "Group", "groupShapes": "Group shapes (Ctrl+G)", "mirrorMode": {"enable": "Enable mirror mode", "active": "Mirror Mode Active", "multiSelectionNotAvailable": "Multi-selection is not available while mirror mode is active"}, "rectangle": "Rectangle", "resetZoom": "Zoom to workspace", "rightTriangle": "Right triangle", "rotate45Clockwise": "Rotate 45° clockwise", "sections": {"grouping": "Grouping tools", "shapes": "Shape tools", "transform": "Transform tools", "utility": "Utility tools"}, "transformTools": "Transform tools", "trapezoid": "Trapezoid", "triangle": "Triangle", "ungroup": "Ungroup", "ungroupShape": "Ungroup shapes (Ctrl+Shift+G)", "utilityTools": "Utility tools"}, "tools": {"draw": "Draw", "erase": "Erase", "move": "Move", "resize": "Resize", "rotate": "Rotate", "select": "Select"}}, "signup": {"success_accountCreated": "Account created successfully", "acceptTerms": "Please accept the terms and conditions...", "confirmPassword": "Confirm password", "confirmPasswordHint": "Confirm your password", "createAccountButton": "Create account", "createPassword": "Create a password", "fullName": "Full name", "fullNameHint": "Enter your full name", "haveAccount": "Already have an account?", "loginLink": "<PERSON><PERSON>", "phoneNumber": "Phone number", "phoneNumberHint": "Enter your phone number", "signupFailed": "Signup failed. Please try again.", "subtitle": "Create an account to get started", "termsAndConditions1": "I agree to the ", "termsAndConditions2": "terms and conditions", "title": "Create account", "validation": {"confirmPasswordRequired": "Please confirm your password", "nameLength": "Name must be at least 2 characters", "nameRequired": "Name is required", "passwordRequirements": {"length": "Password must be at least 8 characters", "number": "Password must contain at least one number", "uppercase": "Password must contain at least one uppercase letter"}, "passwordsMismatch": "Passwords do not match", "phoneNumberInvalid": "Please enter a valid phone number", "phoneNumberRequired": "Phone number is required"}, "welcomeMessage": "Welcome to xoxknit!"}, "swatchCalculator": {"enterMeasurement": "Enter your measurement", "enterSwatchLength": "Enter swatch length", "enterSwatchRows": "Enter swatch rows", "enterSwatchStitches": "Enter swatch stitches", "enterSwatchWeight": "Enter swatch weight", "enterSwatchWidth": "Enter swatch width", "gotIt": "Got it", "infoTooltip": "More info...", "learnMore": "Learn more about @label", "measurements": "MEASUREMENTS", "resultLabels": {"rowsPer10Cm": "Rows per 10cm", "stitchesPer10Cm": "Stitches per 10cm", "weightPer100CmSquared": "Weight per 100cm²"}, "results": "RESULTS", "rowsInSwatch": {"description": "Count the total number of rows from top to bottom of your swatch.", "hint": "Rows", "label": "5 - rows in swatch"}, "stitchesInSwatch": {"description": "Count the total number of stitches across one row of your swatch.", "hint": "Stitches", "label": "4 - stitches in swatch"}, "swatchLength": {"description": "The vertical measurement of your swatch. Count the rows from top to bottom.", "hint": "Length", "label": "2 - swatch length"}, "swatchWeight": {"description": "Weight of your swatch in grams. This helps calculate yarn requirements. It's ok to use the full weight of the swatch (rather than the specific measured dimensions) in order to allow for extra wool required.", "hint": "Weight", "label": "3 - swatch weight"}, "swatchWidth": {"description": "The horizontal measurement of your swatch. Count the stitches from edge to edge.", "hint": "<PERSON><PERSON><PERSON>", "label": "1 - swatch width"}, "title": "Swatch calculator"}, "userMachines": {"addMachine": "Add machine", "addYourFirstMachine": "Add your first knitting machine", "deleteConfirm": {"message": "Are you sure you want to delete this machine? This action cannot be undone.", "title": "Delete machine"}, "editMachine": "Edit machine", "errors": {"deleteFailed": "Failed to delete machine", "loadFailed": "Failed to load machines", "saveFailed": "Failed to save machine"}, "machineDetails": {"gauge": "Gauge", "gaugeHint": "Machine gauge", "name": "Machine name", "nameHint": "Enter a nickname for this machine", "needleBed": "Needle bed", "needleBedHint": "Number of needles", "type": "Machine type", "typeHint": "Select machine type"}, "machineInfo": {"needles": "@count needles", "patternControl": "Pattern control", "pitch": "@pitchmm pitch"}, "noMachines": "No machines configured", "title": "My knitting machines", "machineDialog": {"searchMachines": "Search machines...", "selectYourMachine": "Select your machine from the list below", "all": "All", "noMachinesFound": "No machines found matching your search"}}, "knittingProgress": {"percentComplete": "@percent% complete", "row": "row @number", "needleRangeBoth": "L@left - R@right", "needleRangeLeft": "L1 - L@number", "needleRangeSingleLeft": "L1", "needleRangeRight": "R1 - R@number", "needleRangeSingleRight": "R1", "noActiveNeedles": "No active needles", "dimensionIndicator_stitches": "@count stitches", "dimensionIndicator_rows": "@count rows", "patternCompleted": "🎉 PATTERN COMPLETED! 🎉"}, "customShapeCreator": {"pointsAdded": "@count points added", "cancel": "Cancel", "finished": "Finished", "undo": "Undo", "redo": "Redo", "deleteVertex": "Delete vertex", "selectedVertex": "Vertex selected - tap elsewhere to add point or drag to move", "addMorePoints": "Add at least 3 points to create a custom shape", "instructions": "Tap to add points, tap existing points to select and move them"}, "knittingZone": {"configsSaved": "Knitting zone configurations have been saved successfully.", "controlSettings": "Knitting Control Settings", "emptyZone": "Empty Zone", "noZonesFound": "No knitting zones found. Please generate instructions first.", "noZonesToConfigure": "No knitting zones to configure.", "saveConfiguration": "Save Configuration", "visualization": "Knitting Zones Visualization", "control": "knitting control", "autoControlCarriagePosition": "Auto control carriage position", "startWithCarriageOn": "start this zone with carriage on", "left": "left", "right": "right", "autoAdjustAsymmetricalRows": "Auto adjust slightly asymmetrical rows", "byDecreasingOrIncreasing": "by decreasing or increasing 1 needle", "dec": "dec", "inc": "inc", "autoControlIncreaseDecrease": "Auto control increases and decreases", "increaseBy": "Increase by", "stitchesEvery": "stitches every", "rows": "rows", "decreaseBy": "Decrease by", "back": "<< Back", "settingsApplied": "Settings Applied", "settingsFromApplied": "The settings from @zoneName have been applied to all similar zones.", "applyToAllZones": "Apply to all zones", "applyToAll": "Apply to all", "nextZone": "Next zone >>", "next": "Next >>", "finishingMethodForBottomEdge": "Finishing method for bottom edge", "bindOff": "bind off", "useScrap": "use scrap", "finish": "Finish >>", "off": "off", "on": "on", "configuration": "Zone Configuration", "emptyEdgeZone": "Empty Edge Zone", "rowsCount": "@count rows", "stitchesWide": "@count stitches wide", "completed": "Completed", "pending": "Pending", "zonePosition": {"top": "Top", "middle": "Middle", "bottom": "Bottom", "topEdge": "Top Edge", "bottomEdge": "Bottom Edge"}, "finishingMethod": "Finishing Method", "bindOffMethod": "Bind Off", "useScrapMethod": "Use Scrap", "autoCarriagePositionControl": "Auto Carriage Position Control", "carriageStartsOn": "Carriage starts on @side", "autoAdjustAsymmetricalRowsText": "Auto Adjust Asymmetrical Rows", "adjustBy": "Adjust by @direction", "increasing": "increasing", "decreasing": "decreasing", "autoControlIncreasesDecreasesText": "Auto Control Increases/Decreases", "manualIncreasesDecreases": "Manual Increases/Decreases", "position": "Position", "positionFormat": "Position: @needles · Rows: @rows"}, "zoneConfiguration": {"carriagePosition": {"automatic": "Automatic (Start Right)", "startLeft": "Start Left", "startRight": "Start Right"}, "asymmetricalAdjustment": {"automatic": "Automatic (No Adjustment)", "addStitch": "<PERSON><PERSON> to Shorter Side", "removeStitch": "<PERSON><PERSON><PERSON> from Longer Side"}, "increaseDecrease": {"automatic": "Automatic (Follow Pattern)", "noIncreases": "No Increases", "noDecreases": "No Decreases", "incEvery": "Inc @by every @every rows", "decEvery": "Dec @by every @every rows", "incDecFormat": "@incText, @decText"}}, "interactiveKnitting": {"loadingInstructions": "Loading knitting instructions...", "noPatternAvailable": "No knitting pattern available", "editKnittingZones": "Edit Knitting Zones", "backToShapeEditor": "Back to Shape Editor", "zoneEditor": "Zone Editor", "carriage": "Carriage", "asymmetrical": "Asymmetrical", "shaping": "<PERSON><PERSON>ping"}, "zonesEditor": {"toolbar": {"editMode": {"enter": "Enter Edit Mode", "exit": "Exit Edit Mode"}, "saveNewZone": "Save New Zone", "cancelZoneCreation": "Cancel Zone Creation", "undoLastPoint": "Undo Last Point", "utilityTools": "Utility Tools", "resetZoom": "Reset Zoom", "undo": {"available": "Undo Zone Operation", "withOperation": "Undo Zone Operation: @operation", "unavailable": "Undo (No zone actions to undo)"}, "redo": {"available": "Redo Zone Operation", "withOperation": "Redo Zone Operation: @operation", "unavailable": "Redo (No zone actions to redo)"}}}}