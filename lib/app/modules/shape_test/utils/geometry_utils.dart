import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';

/// Class to hold group bounding box information
class GroupBoundsData {
  final Rect bounds;
  final List<Offset> vertices;
  final Offset center;

  GroupBoundsData({
    required this.bounds,
    required this.vertices,
    required this.center,
  });
}

/// Utility functions for geometry calculations related to shape manipulation
class GeometryUtils {
  /// Calculate a point on a cubic bezier curve at parameter t
  static Offset calculateBezierPoint(
      Offset p0, Offset p1, Offset p2, Offset p3, double t) {
    final mt = 1 - t;
    final mt2 = mt * mt;
    final mt3 = mt2 * mt;
    final t2 = t * t;
    final t3 = t2 * t;

    return Offset(
      mt3 * p0.dx + 3 * mt2 * t * p1.dx + 3 * mt * t2 * p2.dx + t3 * p3.dx,
      mt3 * p0.dy + 3 * mt2 * t * p1.dy + 3 * mt * t2 * p2.dy + t3 * p3.dy,
    );
  }

  /// Constrain an offset within the given constraints
  static Offset constrainOffset(Offset offset, BoxConstraints constraints) {
    return Offset(
      offset.dx.clamp(0, constraints.maxWidth),
      offset.dy.clamp(0, constraints.maxHeight),
    );
  }

  /// Calculate the bounding rectangle for a set of vertices
  static Rect calculateBoundingRect(List<Offset> vertices,
      {double minSize = 20.0}) {
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;

    for (final vertex in vertices) {
      minX = math.min(minX, vertex.dx);
      minY = math.min(minY, vertex.dy);
      maxX = math.max(maxX, vertex.dx);
      maxY = math.max(maxY, vertex.dy);
    }

    final width = math.max(maxX - minX, minSize);
    final height = math.max(maxY - minY, minSize);

    return Rect.fromLTWH(minX, minY, width, height);
  }

  /// Calculate the center point of a rectangle
  static Offset calculateRectCenter(Rect rect) {
    return Offset(
      rect.left + rect.width / 2,
      rect.top + rect.height / 2,
    );
  }

  /// Check if all vertices are within the given constraints
  static bool areVerticesInBounds(
      List<Offset> vertices, BoxConstraints constraints) {
    return vertices.every((vertex) =>
        vertex.dx >= 0 &&
        vertex.dx <= constraints.maxWidth &&
        vertex.dy >= 0 &&
        vertex.dy <= constraints.maxHeight);
  }

  /// Helper to create a transformation matrix for rotation around a center point
  static Matrix4 createRotationMatrix(Offset center, double angle) {
    return Matrix4.identity()
      ..translate(center.dx, center.dy)
      ..rotateZ(angle)
      ..translate(-center.dx, -center.dy);
  }

  /// Calculate the bounding rectangle for a shape, considering curves.
  /// Uses path flattening for accuracy when curves are present.
  /// Returns a Rect for simple shapes or GroupBoundsData for group shapes.
  static dynamic calculateAccurateBoundingRect(ShapeData shapeData,
      {double minSize = 20.0}) {
    // --- Handle Group Shapes Recursively ---
    if (shapeData is GroupShapeData) {
      if (shapeData.childShapes.isEmpty) {
        // Create a minimal rect if group is empty
        final rect = Rect.fromLTWH(shapeData.center.dx - minSize / 2,
            shapeData.center.dy - minSize / 2, minSize, minSize);

        // Create vertices for the minimal rect
        final vertices = [
          Offset(rect.left, rect.top),
          Offset(rect.right, rect.top),
          Offset(rect.right, rect.bottom),
          Offset(rect.left, rect.bottom),
        ];

        return GroupBoundsData(
          bounds: rect,
          vertices: vertices,
          center: rect.center,
        );
      }

      double minX = double.infinity;
      double minY = double.infinity;
      double maxX = -double.infinity;
      double maxY = -double.infinity;

      for (final childShape in shapeData.childShapes) {
        // Recursively calculate the accurate bounds of each child
        final childResult = calculateAccurateBoundingRect(childShape);
        final childBounds = childResult is GroupBoundsData
            ? childResult.bounds
            : childResult as Rect;

        minX = math.min(minX, childBounds.left);
        minY = math.min(minY, childBounds.top);
        maxX = math.max(maxX, childBounds.right);
        maxY = math.max(maxY, childBounds.bottom);
      }

      // Create the combined bounds
      final combinedBounds = Rect.fromLTRB(minX, minY, maxX, maxY);
      final width = math.max(combinedBounds.width, minSize);
      final height = math.max(combinedBounds.height, minSize);
      final accurateBounds =
          Rect.fromLTWH(combinedBounds.left, combinedBounds.top, width, height);

      // Create vertices for the corners of the accurate bounding box
      final vertices = [
        Offset(accurateBounds.left, accurateBounds.top), // Top-left
        Offset(accurateBounds.right, accurateBounds.top), // Top-right
        Offset(accurateBounds.right, accurateBounds.bottom), // Bottom-right
        Offset(accurateBounds.left, accurateBounds.bottom), // Bottom-left
      ];

      // Calculate center based on the accurate bounds
      final groupCenter = Offset(accurateBounds.left + accurateBounds.width / 2,
          accurateBounds.top + accurateBounds.height / 2);

      return GroupBoundsData(
        bounds: accurateBounds,
        vertices: vertices,
        center: groupCenter,
      );
    }
    // --- End Group Shape Handling ---

    // --- Original logic for non-group shapes ---
    final path = _buildShapePath(shapeData);
    Rect bounds;

    // Apply rotation to the path before calculating bounds if necessary
    Path pathToMeasure = path;
    if (shapeData.rotation != 0) {
      final matrix = createRotationMatrix(shapeData.center, shapeData.rotation);
      pathToMeasure = path.transform(matrix.storage);
    }

    // Use flattened bounds calculation if curves exist
    final hasCurves =
        shapeData.curveControls.values.any((offset) => offset != Offset.zero);
    if (hasCurves) {
      bounds = _calculateFlattenedBounds(pathToMeasure);
    } else {
      bounds = pathToMeasure.getBounds();
    }

    // Ensure minimum size
    final width = math.max(bounds.width, minSize);
    final height = math.max(bounds.height, minSize);
    return Rect.fromLTWH(bounds.left, bounds.top, width, height);
  }

  /// Build a path from ShapeData (including curves if present)
  /// This was previously a private method but is now public for reuse in hit testing
  static Path buildShapePath(ShapeData shapeData) {
    final path = Path();
    final vertices = shapeData.vertices;
    if (vertices.isEmpty) return path;

    path.moveTo(vertices.first.dx, vertices.first.dy);

    for (int i = 0; i < vertices.length; i++) {
      final nextIndex = (i + 1) % vertices.length;
      final startPoint = vertices[i];
      final endPoint = vertices[nextIndex];

      final hasControl = shapeData.curveControls.containsKey(i) &&
          shapeData.curveControls[i] != Offset.zero;

      if (hasControl) {
        final controlOffset = shapeData.curveControls[i] ?? Offset.zero;
        final edgeMidpoint = Offset(
          (startPoint.dx + endPoint.dx) / 2,
          (startPoint.dy + endPoint.dy) / 2,
        );
        final controlPoint = edgeMidpoint + controlOffset;
        final edgeVector = endPoint - startPoint;
        final control1 = startPoint + edgeVector * 0.25;
        final control2 = endPoint - edgeVector * 0.25;
        final adjustedControl1 = control1 + (controlPoint - edgeMidpoint) * 0.5;
        final adjustedControl2 = control2 + (controlPoint - edgeMidpoint) * 0.5;
        path.cubicTo(
          adjustedControl1.dx,
          adjustedControl1.dy,
          adjustedControl2.dx,
          adjustedControl2.dy,
          endPoint.dx,
          endPoint.dy,
        );
      } else {
        path.lineTo(endPoint.dx, endPoint.dy);
      }
    }
    path.close();
    return path;
  }

  // --- Private Helper: Build path from ShapeData (similar to ShapePainter) ---
  static Path _buildShapePath(ShapeData shapeData) {
    // Call the public method to avoid duplicating code
    return buildShapePath(shapeData);
  }

  // --- Private Helper: Calculate flattened bounds (copied from ShapeManipulationHandlers) ---
  static Rect _calculateFlattenedBounds(Path path) {
    Rect bounds = path.getBounds(); // Default fallback
    List<Offset> allPoints = [];
    try {
      for (final metric in path.computeMetrics()) {
        final tangent = metric.getTangentForOffset(0);
        if (tangent != null) {
          final List<Offset> points = [];
          // Sample points along the path metric
          for (double d = 0; d <= metric.length; d += 1.0) {
            // Sample density
            final t = metric.getTangentForOffset(d);
            if (t != null) {
              points.add(t.position);
            }
          }
          // Ensure start and end points are included
          final startTangent = metric.getTangentForOffset(0);
          final endTangent = metric.getTangentForOffset(metric.length);
          if (startTangent != null && !points.contains(startTangent.position)) {
            points.insert(0, startTangent.position);
          }
          if (endTangent != null && !points.contains(endTangent.position)) {
            points.add(endTangent.position);
          }
          allPoints.addAll(points);
        } else {
          print(
              "Warning: Could not get tangent. Falling back to getBounds in GeometryUtils.");
          return path.getBounds(); // Early return with fallback
        }
      }
      if (allPoints.isNotEmpty) {
        bounds = _calculateBoundsFromPoints(allPoints);
      }
    } catch (e) {
      print(
          "Error during flattening in GeometryUtils: $e. Falling back to getBounds.");
      bounds = path.getBounds(); // Fallback on error
    }
    return bounds;
  }

  // --- Private Helper: Calculate bounds from points (copied from ShapeManipulationHandlers) ---
  static Rect _calculateBoundsFromPoints(List<Offset> points) {
    if (points.isEmpty) return Rect.zero;
    double minX = points.first.dx, minY = points.first.dy;
    double maxX = points.first.dx, maxY = points.first.dy;
    for (final point in points) {
      minX = math.min(minX, point.dx);
      minY = math.min(minY, point.dy);
      maxX = math.max(maxX, point.dx);
      maxY = math.max(maxY, point.dy);
    }
    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
}
