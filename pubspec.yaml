name: xoxknit
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.4+5

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.6.6
  get_storage: ^2.1.1
  # flutter_launcher_icons: ^0.14.1
  shopify_flutter: ^2.2.8
  google_fonts: ^4.0.4
  animations: ^2.0.11
  firebase_auth: ^5.3.3
  firebase_core: ^3.8.0
  equatable: ^2.0.5
  fluttertoast: ^8.2.12
  flutter_dotenv: ^5.2.1
  cloud_firestore: ^5.5.0
  flutter_spinkit: ^5.2.1
  uuid: ^4.5.1
  font_awesome_flutter: ^10.8.0
  vector_math: ^2.1.4
  flutter_svg: ^2.0.17
  intl: ^0.19.0
  flutter_localizations:
    sdk: flutter
  path_provider: ^2.1.2
  pdf: ^3.11.3
  printing: ^5.14.2
  display_metrics: ^1.0.0
  crypto: ^3.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  mockito: ^5.4.4

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  test: ^1.25.7
  build_runner: ^2.4.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env
    - assets/images/logo.png
    - assets/images/logo_white.png
    - assets/images/help/
    - assets/fonts/Eight_One.ttf
    - assets/locales/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: EightOne
      fonts:
        - asset: assets/fonts/Eight_One.ttf
          weight: 400

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
