# XoxKnit Shape Drawing Board Documentation

## Overview

The Shape Drawing Board is a sophisticated drawing and manipulation system built with Flutter for the XoxKnit knitting design software. It provides a professional grid-based canvas where users can create, edit, and transform various shapes that represent garment pieces or patterns intended for machine knitting. The system includes features like zooming, panning, shape manipulation, grid snapping, and history management for undo/redo operations, all tailored to the specific needs of digital knitting pattern design.

## Knitting Design Context

The Shape Drawing Board exists within the larger context of a knitting machine design application with specific requirements and constraints:

### Machine Knitting Background

1. **Knitting Machines**: Modern knitting machines contain a bed of needles arranged in a row (typically 100-300 needles)
2. **Needle Positioning**: Needles are typically numbered from the center outward, with left (L) and right (R) designations
3. **Row-Based Operations**: Knitting happens row by row, with each row representing a pass of the carriage across the needle bed
4. **Pattern Creation**: Designers need to specify exactly which stitches are worked on which needles for each row

### From Drawing to Knitting

The shapes drawn on the board directly translate to knitting instructions:
- Each column in the grid represents a specific needle on the knitting machine
- Each row in the grid represents a row of knitting
- Shapes outline areas where specific stitch patterns or techniques will be applied
- The dimensions and positions of shapes determine the dimensions of the final knitted piece

### Design Constraints

- Designs must respect the physical limitations of knitting machines (e.g., maximum needle count)
- Shapes must align properly with the needle grid for accurate knitting instructions
- Complex curves may need to be approximated as knitting occurs on a discrete grid of stitches

## System Architecture

The shape drawing board consists of several interconnected components:

### Core Components

1. **GridSystem**: Manages the grid display and snapping functionality, with special consideration for knitting needle spacing
2. **ShapeData Model**: Represents shape information (vertices, type, transforms) that will translate to knitting areas
3. **ZoomableBoard**: Handles zoom and pan transformations
4. **ShapePainter**: Renders shapes with customizable styling
5. **TransformationManager**: Manages shape transformations (flip, rotate)
6. **HistoryManager**: Handles undo/redo operations

### Controllers & Managers

- **ShapeEditorController**: Central controller coordinating the drawing system
- **ShapeManager**: Manages shape creation, selection, and manipulation
- **GridSystem**: Provides grid display and snap functionality
- **TransformationManager**: Handles geometric transformations of shapes

## Grid System

The grid system is the foundation of the drawing board that provides visual guidance and precise positioning, directly corresponding to the physical needles and rows of a knitting machine.

### Grid Types

The system supports multiple grid types:
- **Standard**: Regular rectangular grid (default) - most useful for knitting as it directly maps to needles and rows
- **Isometric**: Grid with diagonal lines at 30° and 150° angles - useful for designing patterns with diagonal elements
- **Radial**: Circular grid with concentric circles and radial lines - useful for circular knitting projects

### Grid Features

- **Customizable grid size**: Base size for grid cells, allowing for different zoom levels while maintaining needle representation
- **Multi-level grid display**: Major, secondary, and regular grid lines with different visibility at various zoom levels
- **Center lines**: Optional display of emphasized center axes, corresponding to the center of the knitting machine
- **Needle-based grid**: Specialized grid for knitting applications with L/R needle notation matching actual machine needle arrangements
- **Adaptive visibility**: Grid elements adapt visibility based on zoom level
- **Grid labels**: Row and needle labels that adapt to zoom level, using knitting-specific notation (L1, L2, R1, R2, etc.)

### Needle-Based Grid

The needle-based grid is a critical feature for the knitting application:

- **Columns as Needles**: Each vertical line in the grid represents a specific needle on the knitting machine
- **L/R Notation**: Needles are labeled from the center outward as L1, L2, etc. (left) and R1, R2, etc. (right), matching standard knitting machine notation
- **Even Needle Count**: The system requires an even number of needles for proper L/R designation
- **Needle Gauge**: The system accounts for the physical needle gauge of the machine (spacing between needles)
- **Grid-to-Needle Mapping**: Internal mapping functions convert between grid coordinates and needle positions

### Grid Labels

Grid labels appear along the top (needles) and left side (rows) of the grid:

- **Needle Labels (Top)**: Labels use L/R convention for needles (L1, L2, R1, R2, etc.) exactly matching the knitting machine's needle designations
- **Row Labels (Left)**: Numbers representing rows of knitting, starting from 1 at the top
- Labels adapt density based on zoom level (showing every nth label)
- Labels fade in/out based on interaction state
- Font size adapts to current zoom level

## Zoom and Pan Functionality

The `ZoomableBoard` widget provides smooth zoom and pan capabilities:

### Features

- **Smooth scaling**: Scale from 0.5x to 5.0x
- **Panning**: Move around the canvas with drag gestures
- **Transformation synchronization**: Grid, shapes, and labels update correctly as user zooms and pans
- **Multi-touch gestures**: Pinch to zoom
- **Interaction state tracking**: System tracks when user is actively zooming/panning

### Implementation Details

- Uses Flutter's `InteractiveViewer` for efficient zoom/pan
- Updates `TransformationController` to reflect current state
- Synchronizes grid system with current transformation
- Maintains proper hit detection regardless of zoom level

## Shape System

In the knitting context, shapes represent areas of distinct stitch patterns, increases, decreases, or other knitting techniques.

### Supported Shape Types

- **Rectangle**: Four-sided shape with right angles - useful for basic panels and rectangular pieces
- **Triangle**: Equilateral triangle with point at top center - useful for shaping areas like shoulder decreases
- **Right Triangle**: Triangle with 90° angle at bottom left - useful for diagonal edges and asymmetrical shaping
- **Trapezoid**: Four-sided shape with inset at top - useful for tapered sections like waist shaping
- **Group**: Collection of shapes grouped as a single unit - useful for complex pattern areas requiring multiple shapes

### Shape Data Model

The `ShapeData` model stores all information needed to render and manipulate shapes:

- **Type**: The shape type (rectangle, triangle, etc.)
- **Vertices**: List of corner points defining the shape
- **Curve Controls**: Map of control points for bezier curves 
- **Bounding Rectangle**: Calculated rectangle encompassing the shape
- **Center**: Center point for transformations
- **Rotation**: Current rotation angle in radians

### Shape Creation

Shapes are created using factory methods that generate appropriate vertices:

```dart
// Example of rectangle creation
ShapeData.rectangle(Rect rect)
```

### Shape Rendering

The `ShapePainter` class handles rendering shapes with the following features:

- **Fill & Stroke**: Shapes are filled with a semi-transparent color and stroked with a solid border
- **Selection Indication**: Selected shapes are highlighted with a different color and stroke width
- **Curved Edges**: Optional bezier curves between vertices
- **Curve Controls**: Visual indicators when in curve edit mode
- **Rotation Handling**: Shapes are properly rendered with rotation applied

## Shape Manipulation

### Selection

- Single or multiple shape selection
- Selection handles for manipulation
- Selected shapes indicate state visually

### Transformation Operations

The `TransformationManager` provides operations for:

- **Horizontal Flip**: Mirror shapes horizontally - useful for creating symmetrical pattern pieces
- **Vertical Flip**: Mirror shapes vertically - useful for creating top-down or bottom-up variations
- **Rotation**: Rotate shapes around their center point - useful for creating angled design elements

### Curve Editing

- Toggle curve mode to edit the curvature of shape edges
- Drag control points to adjust curves
- Snap curves back to straight lines when near zero offset

### Hit Testing

Custom hit testing is provided by the `ShapeHitDetector` class:

- Uses path-based hit detection for accurate shape selection
- Provides touch padding for easier selection
- Handles complex shape outlines correctly

## Snapping System

The grid system provides sophisticated snapping functionality essential for aligning shapes precisely with knitting needles:

### Snapping Behaviors

- **Grid Point Snapping**: Snap to intersections of grid lines - ensures shapes align with specific needles and rows
- **Grid Line Snapping**: Snap to nearest grid line - ensures shapes align with needle and row lines
- **Threshold-based**: Only snap when within defined threshold
- **Grid-Type Aware**: Different snapping behavior for each grid type
- **Adaptive Threshold**: Snap threshold scales with zoom level

### Grid-Specific Snapping

- **Standard Grid**: Snaps to rectangular grid points - most important for knitting as it ensures alignment with needles
- **Isometric Grid**: Snaps to 30°, 90° and 150° lines - useful for diagonal pattern elements
- **Radial Grid**: Snaps to concentric circles and radial lines - useful for circular knitting designs
- **Needle Grid**: Snaps to vertical needles and horizontal rows - critical for ensuring designs map correctly to physical needles

## Translation to Knitting Instructions

The ultimate purpose of the shape drawing board is to generate knitting instructions:

### From Shape to Stitch

- **Shape Boundaries**: Define where specific stitch patterns or techniques are applied
- **Column-to-Needle Mapping**: The system automatically maps grid columns to specific needles
- **Row Mapping**: Vertical positions in the grid translate to specific rows of knitting
- **Shape Intersections**: Where shapes intersect grid lines determines which stitches are worked in each row

### Knitting Machine Compatibility

- The system can be configured with specific needle counts to match different knitting machines
- The L/R needle notation matches standard knitting machine conventions
- Grid spacing can be adjusted to represent different machine gauges (e.g., standard gauge, mid-gauge, bulky)

### Pattern Generation

While not part of the drawing board itself, the shapes created become the foundation for:
- Generating row-by-row knitting instructions
- Calculating stitch counts for each section
- Producing machine-readable pattern files
- Creating visual knitting charts

## History Management

The `HistoryManager` provides undo/redo functionality:

### Features

- **Operation Recording**: Records state changes for undo/redo
- **Change Tracking**: Tracks changes between operations
- **Complex Operations**: Support for multi-step operations
- **Bounded History**: Maintains a limited history size (50 entries)
- **Named Operations**: Tracks operation names for UI feedback

## Potential Issues and Limitations

### Performance Considerations

1. **Large Shape Count**: Performance may degrade with a very large number of shapes
2. **Complex Shape Rendering**: Shapes with many vertices or curves may affect performance
3. **Grid Rendering at Extreme Zoom**: Very high/low zoom levels may cause visual artifacts

### Edge Cases

1. **Zero-Size Shapes**: Creating shapes with zero or negative dimensions may cause errors
2. **Rotation Limits**: Extreme rotation values may cause numerical issues
3. **Grid Boundaries**: Shapes positioned far from the origin may have snapping issues
4. **Curve Control Limits**: Moving curve controls too far may cause rendering artifacts

### Knitting-Specific Limitations

1. **Curve Approximation**: Smooth curves in designs must ultimately be translated to the discrete needle grid
2. **Machine Constraints**: Different knitting machines have different needle counts and capabilities
3. **Complex Pattern Areas**: Very complex pattern areas might require multiple overlapping shapes
4. **Gauge Limitations**: The system may not perfectly represent all machine gauges in the visual interface

### Known Limitations

1. **Group Shape Limitations**: 
   - Nested groups may have unexpected behavior
   - Group transformations might not perfectly preserve inner shape positions
   
2. **Grid System Limitations**:
   - Isometric grid snapping accuracy decreases at very low zoom levels
   - Needle count must be an even number for proper L/R numbering

3. **Zoom Constraints**:
   - Minimum zoom of 0.5x and maximum of 5.0x
   - Very large zooms may cause visual jitter in grid lines

## Implementation Tips

1. Ensure shape vertices are always in a valid state
2. Update the grid system when changing zoom or pan
3. Use the history manager to track all state-changing operations
4. Consider performance when working with many shapes or complex curves
5. Handle edge cases like empty shapes or invalid transformations
6. For knitting applications, always verify shapes align properly with the needle grid
7. Consider machine limitations when designing complex shapes
8. Ensure needle count configuration matches the target knitting machine

## Example Usage

```dart
// Create a grid system for a standard knitting machine with 200 needles
final gridSystem = GridSystem(
  gridSize: 20.0,
  gridType: GridType.standard,
  snapToGrid: true,
  needleCount: 200, // Represents a machine with 200 needles
  needleGaugeCm: 0.45, // Standard gauge machine (4.5mm needle spacing)
);

// Create a zoomable board
ZoomableBoard(
  controller: ShapeEditorController,
  child: ProfessionalGridWidget(
    gridSystem: gridSystem,
    enableGestures: false, // Gestures handled by ZoomableBoard
    child: KnittingPatternsContainer(), // Container for pattern shapes
  ),
);
```

This documentation provides a comprehensive overview of the shape drawing board system, its components, and functionality within the context of a knitting design application. With this information, developers should be able to understand how the system translates visual designs into knitting instructions, as well as use and troubleshoot the system effectively. 